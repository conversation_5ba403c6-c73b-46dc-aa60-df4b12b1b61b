using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Categories;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    public class CategoryApiRepository : BaseApiRepository, ICategoryApiRepository
    {
        public CategoryApiRepository(HttpClient httpClient, ILogger<CategoryApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع التصنيفات
        /// </summary>
        public async Task<(IEnumerable<CategoryDTO>? list, ResponseVM? response)> GetAllCategoriesAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع التصنيفات");
                var response = await GetAsync("Categories/getAllCategories");

                if (response.IsSuccessStatusCode)
                {
                    var categories = await response.Content.ReadFromJsonAsync<CategoryDTO[]>();
                    return (categories, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب التصنيفات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب تصنيف بالمعرف
        /// </summary>
        public async Task<(CategoryDTO? model, ResponseVM? response)> GetCategoryByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب التصنيف بالمعرف: {Id}", id);
                var response = await GetAsync($"Categories/getCategoryById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var category = await response.Content.ReadFromJsonAsync<CategoryDTO>();
                    return (category, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب التصنيف بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة تصنيف جديد
        /// </summary>
        public async Task<ResponseVM> InsertCategoryAsync(CategoryDTO category)
        {
            try
            {
                _logger.LogInformation("إضافة تصنيف جديد: {Name}", category.Name);
                var response = await PostAsJsonAsync("Categories/insertCategory", category);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة التصنيف بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة التصنيف: {Name}", category.Name);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث تصنيف موجود
        /// </summary>
        public async Task<ResponseVM> UpdateCategoryAsync(Guid id, CategoryDTO category)
        {
            try
            {
                _logger.LogInformation("تحديث التصنيف: {Id}", id);
                var response = await PutAsJsonAsync($"Categories/updateCategory/{id}", category);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث التصنيف بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث التصنيف: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف تصنيف
        /// </summary>
        public async Task<ResponseVM> DeleteCategoryAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف التصنيف: {Id}", id);
                var response = await DeleteAsync($"Categories/deleteCategory/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف التصنيف بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف التصنيف: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}

using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Clients;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IClientApiRepository
    {
        /// <summary>
        /// جلب جميع العملاء
        /// </summary>
        Task<(IEnumerable<ClientDTO>? list, ResponseVM? response)> GetAllClientsAsync();

        /// <summary>
        /// جلب جميع الموردين
        /// </summary>
        Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllSuppliersAsync();

        /// <summary>
        /// جلب جميع الزبائن
        /// </summary>
        Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllCustomersAsync();

        /// <summary>
        /// جلب جميع الموردين للكومبو بوكس
        /// </summary>
        Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllSuppliersCMAsync();

        /// <summary>
        /// جلب عميل بالمعرف
        /// </summary>
        Task<(ClientDTO? model, ResponseVM? response)> GetClientByIdAsync(Guid id);

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        Task<ResponseVM> InsertClientAsync(ClientDTO client);

        /// <summary>
        /// تحديث عميل موجود
        /// </summary>
        Task<ResponseVM> UpdateClientAsync(Guid id, ClientDTO client);

        /// <summary>
        /// حذف عميل
        /// </summary>
        Task<ResponseVM> DeleteClientAsync(Guid id);
    }
}

﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Users;
using PosGTech.Web.Services;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Receipts;

public partial class UpsertReceiptSells
{
    [Inject]
    IdentityAuthenticationStateProvider _auth { get; set; }
    [Parameter]
    public IEnumerable<UserTreasuryCMDTO> UserTreasury { get; set; } = new List<UserTreasuryCMDTO>();
    [Parameter]
    public IEnumerable<UserCMDTO> Users { get; set; } = new List<UserCMDTO>();
    [Inject]
    IUnitOfWork _unitOfWork { get; set; }
    UserCMDTO _crruntUser;
    [Parameter]
    public EventCallback<ReceiptDTO> ReceiptChanged { get; set; }
    [Parameter]
    public ReceiptDTO Receipt
    {
        get => _receipt;
        set
        {
            if (value == _receipt)
                return;

            _receipt = value;
            if (ReceiptChanged.HasDelegate)
            {
                ReceiptChanged.InvokeAsync(_receipt);
            }
        }
    }
    private ReceiptDTO _receipt;
    private decimal _lastValue = 0;
    protected override async Task OnInitializedAsync()
    {
        if (Receipt.Id == Guid.Empty)
        {
            _lastValue = 0;
            Receipt.IsExchange = true;
            if (Receipt.Sell == null) Receipt.Sell = new() { Client = new() };
            else Receipt.Client = Receipt.Sell.Client;
            var auth = await _auth.GetAuthenticationStateAsync();
            var userIdClaim = auth.User.Claims.First(x => x.Type == "id").Value;
            _crruntUser = Users.First(x => x.Id == Guid.Parse(userIdClaim));
            Receipt.UserTreasury = UserTreasury.First(x => x.User.Id == _crruntUser.Id && x.Treasury.Name == "النقدية");
            StateHasChanged();
        }
        else
        {
            _lastValue = Receipt.Value;
            _crruntUser = Receipt.UserTreasury.User;
            StateHasChanged();
        }

    }
    private async Task<IEnumerable<UserTreasuryCMDTO>> SearchUserTreasury(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id);
        return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id).Where(x => x.Treasury.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    private async Task<IEnumerable<UserCMDTO>> SearchUser(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Users;
        return Users.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    void ValueChange(decimal value)
    {
        Receipt.Value = value;
        if (value < 0)
        {
            Receipt.Value = 0;
            _snackbar.Add("الرجاء ادخال قيمة صحيحة", Severity.Error);
            return;
        }
        if (Receipt.UserTreasury == null)
        {
            Receipt.Value = 0;
            _snackbar.Add("الرجاء اختيار خزينة", Severity.Error);
            return;
        }
        if (Receipt.Sell.Id == Guid.Empty)
        {
            Receipt.Value = 0;
            _snackbar.Add("الرجاء اختيار الفاتورة", Severity.Error);
            return;
        }
        if (Receipt.Sell.FinalTotal - Receipt.Sell.Paid + _lastValue < value)
        {
            Receipt.Value = Receipt.Sell.FinalTotal - Receipt.Sell.Paid + _lastValue;
            value = Receipt.Value;
            _snackbar.Add("تجاوزت قيمة الفاتورة", Severity.Error);

        }

        if (Receipt.UserTreasury.Balance + _lastValue < value)
        {
            Receipt.Value = Receipt.UserTreasury.Balance + _lastValue;
            _snackbar.Add("تجاوزت القيمة الموجودة في الخزينة", Severity.Error);
            return;
        }
    }
    async void OnKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter") await GetSell();
    }
    async Task GetSell()
    {
        var res = await _unitOfWork.Sell.GetSellByNumAsync(Receipt.Sell.InvoiceNo);
        if (res.response == null)
        {
            Receipt.Sell = res.model;
            Receipt.Client = Receipt.Sell.Client;
            StateHasChanged();
        }
        else _snackbar.Add("رقم الفاتورة غير صحيح", Severity.Error); return;
    }
}
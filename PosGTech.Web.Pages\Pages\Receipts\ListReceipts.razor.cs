﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Receipts
{
    public partial class ListReceipts
    {
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        [Inject]
        IDialogService DialogService { get; set; }
        IEnumerable<ReceiptDTO> receipts = new List<ReceiptDTO>();
        ReceiptDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameReceiptForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();
        private bool FilterFunc1(ReceiptDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(ReceiptDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Financial?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.Client?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.UserTreasury?.Treasury?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.UserTreasury?.User?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.ToTreasury?.Treasury?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.ToTreasury?.User?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            if (element.ReceiptNo.ToString() == searchString)
                return true;
            if (element.Employee?.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                return true;
            return false;
        }
        async void Upsert(Guid id, Guid? financialId)
        {
            var parameters = new DialogParameters<UpsertReceipt>();
            parameters.Add(x => x.id, id);
            parameters.Add(x => x.financialId, financialId);
            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertReceipt>(id != Guid.Empty ? "تعديل الإيصال" : "إضافة الإيصال", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(ReceiptDTO obj)
        {
            NameReceiptForDelete = obj.ReceiptNo.ToString();
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Receipt.DeleteReceiptAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _unitOfWork.Receipt.GetAllReceiptsAsync();
            if (res.response == null) receipts = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}
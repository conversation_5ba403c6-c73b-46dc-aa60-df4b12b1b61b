﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Categories;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Categories
{
    public partial class ListCategories
    {
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        [Inject]
        IDialogService DialogService { get; set; }
        IEnumerable<CategoryDTO> categories = new List<CategoryDTO>();
        CategoryDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameCategoryForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();
        private bool FilterFunc1(CategoryDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(CategoryDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertCategory>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertCategory>(id != Guid.Empty ? "تعديل تصنيف" : "إضافة تصنيف", parameters, options).Result;
            if ((string?)result.Data != null) await LoadingData();
        }

        async void Delete(CategoryDTO obj)
        {
            NameCategoryForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Category.DeleteCategoryAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _unitOfWork.Category.GetAllCategoriesAsync();
            if (res.response == null) categories = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}
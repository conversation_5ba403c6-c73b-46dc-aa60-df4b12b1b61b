# إصلاح مشكلتي حقل سعر البيع في فواتير المشتريات

## المشاكل المبلغ عنها

### المشكلة الأولى: عدم جلب سعر البيع عند اختيار الصنف
- عند اختيار صنف من القائمة المنسدلة في فاتورة المشتريات
- حقل سعر البيع يبقى فارغاً ولا يتم تعبئته تلقائياً بسعر البيع المحفوظ للصنف
- **المطلوب**: عند اختيار صنف، يجب أن يتم جلب سعر البيع الحالي للصنف وعرضه في الحقل

### المشكلة الثانية: عدم مسح حقل سعر البيع بعد الحفظ
- بعد حفظ فاتورة المشتريات بنجاح
- حقل سعر البيع لا يتم مسحه/تفريغه
- **المطلوب**: بعد حفظ الفاتورة، يجب مسح جميع الحقول بما في ذلك حقل سعر البيع للاستعداد لإدخال فاتورة جديدة

## تحليل المشكلة

### الملف المتأثر:
`PosGTech.Web.Pages\Pages\Purchases\UpsertPurchase.razor.cs`

### المتغيرات ذات الصلة:
- `SalePriceChange`: متغير يحتوي على سعر البيع المدخل
- `selectedPurchaseItem`: كائن الصنف المحدد حالياً
- `selectedPurchaseItem.ItemUnit.SalePrice`: سعر البيع المحفوظ للوحدة

### الدوال المتأثرة:
1. `OnItemSelected()` - معالج اختيار الصنف من القائمة المنسدلة
2. `ChangeItem()` - دالة تغيير الصنف
3. `Upsert()` - دالة حفظ الفاتورة
4. `NewPurchase()` - دالة إنشاء فاتورة جديدة
5. `AddItemPurchase()` - دالة إضافة صنف للفاتورة

## الحلول المطبقة

### 1. إصلاح المشكلة الأولى: جلب سعر البيع عند اختيار الصنف

#### أ. في دالة `OnItemSelected()`:
```csharp
private async Task OnItemSelected(ItemCMDTO? item)
{
    selectedPurchaseItem.Item = item;
    selectedPurchaseItem.ItemUnit = null;

    if (selectedPurchaseItem.Item != null)
    {
        var fullItem = items.FirstOrDefault(x => x.Id == selectedPurchaseItem.Item.Id);
        if (fullItem != null)
        {
            selectedPurchaseItem.Price = fullItem.CostPrice;
            selectedPurchaseItem.ItemUnit = fullItem.ItemUnits.FirstOrDefault(x => x.IsBasicUnit);
            _dateExp = fullItem.IsHaveExp ? DateTime.Now : null;

            // إضافة سعر البيع عند اختيار الصنف
            SalePriceChange = selectedPurchaseItem.ItemUnit?.SalePrice ?? 0;

            _searchText = item.Name;
        }
    }
    else
    {
        _searchText = string.Empty;
        // مسح سعر البيع عند مسح الاختيار
        SalePriceChange = 0;
    }

    StateHasChanged();
}
```

#### ب. في دالة `ChangeItem()`:
```csharp
private async Task ChangeItem(ItemCMDTO? Item)
{
    selectedPurchaseItem.Item = Item;
    selectedPurchaseItem.ItemUnit = null;

    if (selectedPurchaseItem.Item != null)
    {
        var fullItem = items.FirstOrDefault(x => x.Id == selectedPurchaseItem.Item.Id);
        if (fullItem != null)
        {
            selectedPurchaseItem.Price = fullItem.CostPrice;
            selectedPurchaseItem.ItemUnit = fullItem.ItemUnits.FirstOrDefault(x => x.IsBasicUnit);
            _dateExp = fullItem.IsHaveExp ? DateTime.Now : null;

            // إضافة سعر البيع عند اختيار الصنف
            SalePriceChange = selectedPurchaseItem.ItemUnit?.SalePrice ?? 0;

            _searchText = Item.Name;
        }
    }
    else
    {
        _searchText = string.Empty;
        // مسح سعر البيع عند مسح الاختيار
        SalePriceChange = 0;
    }

    StateHasChanged();
}
```

### 2. إصلاح المشكلة الثانية: مسح حقل سعر البيع بعد الحفظ

#### أ. في دالة `Upsert()` (بعد الحفظ الناجح):
```csharp
if (response.State)
{
    ShowSuccessMessage("تم الحفظ بنجاح");
    await GetPurchase(id == Guid.Empty ? Guid.Parse(response.Message) : id);
    await GetPurchasesNum();
    await GetItems();
    
    // مسح حقول إدخال الصنف الجديد بعد الحفظ الناجح
    selectedPurchaseItem = new PurchaseItemDTO();
    SalePriceChange = 0;
    _searchText = string.Empty;
    
    // إعادة التركيز على حقل البحث للاستعداد لإدخال صنف جديد
    if (ItemForAdd != null)
    {
        await ItemForAdd.FocusAsync();
    }
}
```

#### ب. في دالة `NewPurchase()`:
```csharp
async Task NewPurchase()
{
    if (_isLoading) return;

    try
    {
        SetButtonLoadingState("new", true);

        purchase = new();
        _datePurchase = DateTime.Now;
        id = new();

        // مسح حقول الصنف المحدد وسعر البيع
        selectedPurchaseItem = new PurchaseItemDTO();
        SalePriceChange = 0;
        _searchText = string.Empty;

        await GetItems();

        ShowSuccessMessage("تم إنشاء فاتورة شراء جديدة");
    }
    catch (Exception ex)
    {
        ShowErrorMessage("حدث خطأ أثناء إنشاء فاتورة شراء جديدة. يرجى المحاولة مرة أخرى.");
    }
    finally
    {
        SetButtonLoadingState("new", false);
    }
}
```

#### ج. في دالة `AddItemPurchase()` (بعد إضافة الصنف):
```csharp
index = -2;
items.First(x => x.Id == selectedPurchaseItem.Item.Id).Quantity += ItemExtensions.GetQuantityUnitDTO(selectedPurchaseItem.ItemUnit, selectedPurchaseItem.Quantity);
selectedPurchaseItem = new();
// مسح سعر البيع بعد إضافة الصنف
SalePriceChange = 0;
purchase.Total = purchase.PurchaseItemDTOs.Sum(x => x.Price * x.Quantity);
ItemForAdd.Text = null;
await ItemForAdd.FocusAsync();
ChangeDiscount();
```

## النتائج المتوقعة

### قبل الإصلاح:
**المشكلة الأولى**:
- اختيار صنف → حقل سعر البيع يبقى فارغاً
- المستخدم يحتاج لإدخال سعر البيع يدوياً

**المشكلة الثانية**:
- حفظ الفاتورة → حقل سعر البيع يبقى معبأً بالقيمة السابقة
- المستخدم يحتاج لمسح الحقل يدوياً قبل إدخال فاتورة جديدة

### بعد الإصلاح:
**المشكلة الأولى**:
- اختيار صنف → حقل سعر البيع يتم تعبئته تلقائياً بسعر البيع المحفوظ للصنف
- توفير الوقت والجهد للمستخدم

**المشكلة الثانية**:
- حفظ الفاتورة → جميع الحقول يتم مسحها تلقائياً
- الاستعداد الفوري لإدخال فاتورة جديدة

## اختبار الإصلاح

### سيناريو الاختبار الأول: جلب سعر البيع
1. فتح صفحة فواتير المشتريات
2. اختيار صنف من القائمة المنسدلة
3. **التحقق**: حقل سعر البيع يتم تعبئته تلقائياً بسعر البيع المحفوظ للصنف
4. تغيير الوحدة (إذا كان للصنف وحدات متعددة)
5. **التحقق**: سعر البيع يتم تحديثه حسب الوحدة المختارة

### سيناريو الاختبار الثاني: مسح حقل سعر البيع
1. اختيار صنف وتعبئة جميع الحقول
2. إضافة الصنف للفاتورة
3. **التحقق**: حقل سعر البيع يتم مسحه بعد إضافة الصنف
4. حفظ الفاتورة
5. **التحقق**: جميع الحقول بما في ذلك سعر البيع يتم مسحها
6. النقر على "فاتورة جديدة"
7. **التحقق**: جميع الحقول فارغة ومستعدة للإدخال

### سيناريو الاختبار الثالث: حالات حدية
1. اختيار صنف ثم مسح الاختيار
2. **التحقق**: حقل سعر البيع يتم مسحه
3. اختيار صنف بدون سعر بيع محدد
4. **التحقق**: حقل سعر البيع يظهر 0
5. اختيار صنف بوحدات متعددة
6. **التحقق**: سعر البيع يتغير حسب الوحدة المختارة

## معايير النجاح

✅ **الاختبار ناجح إذا**:
- حقل سعر البيع يتم تعبئته تلقائياً عند اختيار الصنف
- سعر البيع يتطابق مع السعر المحفوظ في قاعدة البيانات
- حقل سعر البيع يتم مسحه بعد إضافة الصنف للفاتورة
- جميع الحقول يتم مسحها بعد حفظ الفاتورة
- جميع الحقول يتم مسحها عند إنشاء فاتورة جديدة

❌ **الاختبار فاشل إذا**:
- حقل سعر البيع يبقى فارغاً بعد اختيار الصنف
- سعر البيع لا يتطابق مع السعر المحفوظ
- حقل سعر البيع لا يتم مسحه بعد العمليات
- ظهور أخطاء في واجهة المستخدم

## الملفات المعدلة

1. `PosGTech.Web.Pages\Pages\Purchases\UpsertPurchase.razor.cs`
   - دالة `OnItemSelected()` - إضافة تعيين سعر البيع
   - دالة `ChangeItem()` - إضافة تعيين سعر البيع
   - دالة `Upsert()` - إضافة مسح الحقول بعد الحفظ
   - دالة `NewPurchase()` - إضافة مسح الحقول
   - دالة `AddItemPurchase()` - إضافة مسح سعر البيع

## التأثير على النظام

### إيجابي:
- تحسين تجربة المستخدم
- توفير الوقت في إدخال البيانات
- تقليل الأخطاء البشرية
- سلاسة في العمل مع فواتير المشتريات

### لا يوجد تأثير سلبي:
- التغييرات محدودة ولا تؤثر على منطق العمل الأساسي
- لا تؤثر على قاعدة البيانات أو العمليات الحسابية
- متوافقة مع الكود الموجود

## ملاحظات إضافية

1. **التوافق مع الوحدات المتعددة**: الحل يدعم الأصناف ذات الوحدات المتعددة
2. **الأمان**: لا يؤثر على أمان النظام أو صحة البيانات
3. **الأداء**: تحسين طفيف في الأداء بسبب تقليل الإدخال اليدوي
4. **القابلية للصيانة**: الكود واضح ومعلق بالعربية لسهولة الصيانة

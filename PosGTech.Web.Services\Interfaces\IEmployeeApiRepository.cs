using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Employees;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// واجهة مستودع API للموظفين - تحتوي على جميع العمليات المطلوبة للتعامل مع الموظفين
    /// </summary>
    public interface IEmployeeApiRepository
    {
        /// <summary>
        /// جلب جميع الموظفين
        /// </summary>
        /// <returns>قائمة الموظفين مع حالة الاستجابة</returns>
        Task<(IEnumerable<EmployeeDTO>? list, ResponseVM? response)> GetAllEmployeesAsync();

        /// <summary>
        /// جلب جميع الموظفين للقائمة المنسدلة
        /// </summary>
        /// <returns>قائمة الموظفين المبسطة مع حالة الاستجابة</returns>
        Task<(IEnumerable<EmployeeCMDTO>? list, ResponseVM? response)> GetAllEmployeesCMAsync();

        /// <summary>
        /// جلب موظف بالمعرف
        /// </summary>
        /// <param name="id">معرف الموظف</param>
        /// <returns>بيانات الموظف مع حالة الاستجابة</returns>
        Task<(EmployeeDTO? model, ResponseVM? response)> GetEmployeeByIdAsync(Guid id);

        /// <summary>
        /// إضافة موظف جديد
        /// </summary>
        /// <param name="employee">بيانات الموظف الجديد</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> InsertEmployeeAsync(EmployeeDTO employee);

        /// <summary>
        /// تحديث موظف موجود
        /// </summary>
        /// <param name="id">معرف الموظف</param>
        /// <param name="employee">بيانات الموظف المحدثة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> UpdateEmployeeAsync(Guid id, EmployeeDTO employee);

        /// <summary>
        /// حذف موظف
        /// </summary>
        /// <param name="id">معرف الموظف</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> DeleteEmployeeAsync(Guid id);
    }
}

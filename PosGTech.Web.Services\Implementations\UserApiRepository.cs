using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمستخدمين - يحتوي على جميع العمليات المطلوبة للتعامل مع المستخدمين
    /// </summary>
    public class UserApiRepository : BaseApiRepository, IUserApiRepository
    {
        public UserApiRepository(HttpClient httpClient, ILogger<UserApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع المستخدمين
        /// </summary>
        public async Task<(IEnumerable<UserDTO>? list, ResponseVM? response)> GetAllUsersAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المستخدمين");
                var response = await GetAsync("Users/getAllUsers");

                if (response.IsSuccessStatusCode)
                {
                    var users = await response.Content.ReadFromJsonAsync<UserDTO[]>();
                    return (users, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المستخدمين");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع المستخدمين للقائمة المنسدلة
        /// </summary>
        public async Task<(IEnumerable<UserCMDTO>? list, ResponseVM? response)> GetAllUsersCMAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المستخدمين للقائمة المنسدلة");
                var response = await GetAsync("Users/getAllUsersCM");

                if (response.IsSuccessStatusCode)
                {
                    var users = await response.Content.ReadFromJsonAsync<UserCMDTO[]>();
                    return (users, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع المستخدمين للقائمة المنسدلة");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب مستخدم بالمعرف
        /// </summary>
        public async Task<(UserDTO? model, ResponseVM? response)> GetUserByIdAsync(string id)
        {
            try
            {
                _logger.LogInformation("جلب المستخدم بالمعرف: {Id}", id);
                var response = await GetAsync($"Users/getUserById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var user = await response.Content.ReadFromJsonAsync<UserDTO>();
                    return (user, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المستخدم بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        public async Task<ResponseVM> InsertUserAsync(UserDTO user)
        {
            try
            {
                _logger.LogInformation("إضافة مستخدم جديد: {UserName}", user.UserName);
                var response = await PostAsJsonAsync("Users/insertUser", user);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة المستخدم بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المستخدم: {UserName}", user.UserName);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث مستخدم موجود
        /// </summary>
        public async Task<ResponseVM> UpdateUserAsync(string id, UserDTO user)
        {
            try
            {
                _logger.LogInformation("تحديث المستخدم: {Id}", id);
                var response = await PutAsJsonAsync($"Users/updateUser/{id}", user);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث المستخدم بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المستخدم: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        public async Task<ResponseVM> DeleteUserAsync(string id)
        {
            try
            {
                _logger.LogInformation("حذف المستخدم: {Id}", id);
                var response = await DeleteAsync($"Users/deleteUser/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف المستخدم بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المستخدم: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        public async Task<(LoginResponseDTO? response, ResponseVM? error)> LoginAsync(LoginDTO loginModel)
        {
            try
            {
                _logger.LogInformation("محاولة تسجيل دخول للمستخدم: {UserName}", loginModel.UserName);
                var response = await PostAsJsonAsync("Users/login", loginModel);

                if (response.IsSuccessStatusCode)
                {
                    var loginResponse = await response.Content.ReadFromJsonAsync<LoginResponseDTO>();
                    return (loginResponse, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الدخول للمستخدم: {UserName}", loginModel.UserName);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public async Task<ResponseVM> LogoutAsync()
        {
            try
            {
                _logger.LogInformation("تسجيل خروج المستخدم");
                var response = await PostAsync("Users/logout", new StringContent("", System.Text.Encoding.UTF8, "application/json"));

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تسجيل الخروج بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الخروج");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        public async Task<ResponseVM> ChangePasswordAsync(ChangePasswordDTO changePasswordModel)
        {
            try
            {
                _logger.LogInformation("تغيير كلمة المرور للمستخدم");
                var response = await PostAsJsonAsync("Users/changePassword", changePasswordModel);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تغيير كلمة المرور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير كلمة المرور");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب خزائن المستخدم
        /// </summary>
        public async Task<(IEnumerable<UserTreasuryCMDTO>? list, ResponseVM? response)> GetAllTreasuriesForUserAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("جلب خزائن المستخدم: {UserId}", userId);
                var response = await GetAsync($"Users/getAllTreasuriesForUser/{userId}");

                if (response.IsSuccessStatusCode)
                {
                    var treasuries = await response.Content.ReadFromJsonAsync<IEnumerable<UserTreasuryCMDTO>>();
                    return (treasuries, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب خزائن المستخدم: {UserId}", userId);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}

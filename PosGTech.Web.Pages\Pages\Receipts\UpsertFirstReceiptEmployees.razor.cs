﻿using Microsoft.AspNetCore.Components;
using PosGTech.ModelsDTO.Employees;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Receipts;

public partial class UpsertFirstReceiptEmployees
{

    IEnumerable<EmployeeCMDTO> Employees = new List<EmployeeCMDTO>();
    [Inject]
    IUnitOfWork _unitOfWork { get; set; }

    [Parameter]
    public EventCallback<ReceiptDTO> ReceiptChanged { get; set; }
    [Parameter]
    public ReceiptDTO Receipt
    {
        get => _receipt;
        set
        {
            if (value == _receipt)
                return;

            _receipt = value;
            if (ReceiptChanged.HasDelegate)
            {
                ReceiptChanged.InvokeAsync(_receipt);
            }
        }
    }
    private ReceiptDTO _receipt;
    protected override async Task OnInitializedAsync()
    {
        var resEmployee = await _unitOfWork.Employee.GetAllEmployeesCMAsync();
        if (resEmployee.response == null) Employees = resEmployee.list;
        Receipt.IsExchange = true;
    }
    private async Task<IEnumerable<EmployeeCMDTO>> SearchEmployee(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Employees;
        return Employees.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }


}
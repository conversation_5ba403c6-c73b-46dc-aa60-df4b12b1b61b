﻿
using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Inventories;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Inventories
{
    public partial class ListInventory
    {
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        IEnumerable<InventoryDTO> inventories = new List<InventoryDTO>();
        InventoryDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameInventoryForDelete;
        MudMessageBox mbox { get; set; }
        private List<BreadcrumbItem> _items = new List<BreadcrumbItem>
        {
         new BreadcrumbItem("الرئيسية", href:"", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("الفواتير الجرد", href: null, disabled: true, icon: Icons.Material.Filled.AccountBalance)
        };

        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
        }
        private bool FilterFunc1(InventoryDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(InventoryDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.InvoiceNo.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            if (element.Date.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        void Upsert(Guid id)
        {
            _navigation.NavigateTo($"/upsertInventory/{id}");
        }
        async void Delete(InventoryDTO obj)
        {
            NameInventoryForDelete = obj.InvoiceNo.ToString();
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Inventory.DeleteInventoryAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _unitOfWork.Inventory.GetAllInventoriesAsync();
            if (res.response == null) inventories = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Treasury;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IUserApiRepository
    {
        /// <summary>
        /// جلب جميع المستخدمين
        /// </summary>
        Task<(IEnumerable<UserDTO>? list, ResponseVM? response)> GetAllUsersAsync();

        /// <summary>
        /// جلب جميع المستخدمين للقائمة المنسدلة
        /// </summary>
        Task<(IEnumerable<UserCMDTO>? list, ResponseVM? response)> GetAllUsersCMAsync();

        /// <summary>
        /// جلب مستخدم بالمعرف
        /// </summary>
        Task<(UserDTO? model, ResponseVM? response)> GetUserByIdAsync(string id);

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        Task<ResponseVM> InsertUserAsync(UserDTO user);

        /// <summary>
        /// تحديث مستخدم موجود
        /// </summary>
        Task<ResponseVM> UpdateUserAsync(string id, UserDTO user);

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        Task<ResponseVM> DeleteUserAsync(string id);

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        Task<(LoginResponseDTO? response, ResponseVM? error)> LoginAsync(LoginDTO loginModel);

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        Task<ResponseVM> LogoutAsync();

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        Task<ResponseVM> ChangePasswordAsync(ChangePasswordDTO changePasswordModel);

        /// <summary>
        /// جلب خزائن المستخدم
        /// </summary>
        Task<(IEnumerable<UserTreasuryCMDTO>? list, ResponseVM? response)> GetAllTreasuriesForUserAsync(Guid userId);
    }
}

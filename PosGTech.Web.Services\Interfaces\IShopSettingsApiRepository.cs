using Microsoft.AspNetCore.Components.Forms;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.ShopSettings;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IShopSettingsApiRepository
    {
        /// <summary>
        /// جلب جميع إعدادات المتجر
        /// </summary>
        Task<(IEnumerable<ShopSettingsDTO>? list, ResponseVM? response)> GetAllShopSettingsAsync();

        /// <summary>
        /// جلب إعدادات المتجر بالمعرف
        /// </summary>
        Task<(ShopSettingsDTO? model, ResponseVM? response)> GetShopSettingsByIdAsync(Guid id);

        /// <summary>
        /// جلب إعدادات المتجر
        /// </summary>
        Task<(ShopSettingsDTO? model, ResponseVM? response)> GetShopSettingsAsync();

        /// <summary>
        /// إدراج إعدادات المتجر
        /// </summary>
        Task<ResponseVM> InsertShopSettingsAsync(ShopSettingsDTO shopSettings);

        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        Task<ResponseVM> UpdateShopSettingsAsync(Guid id, ShopSettingsDTO shopSettings);

        /// <summary>
        /// حذف إعدادات المتجر
        /// </summary>
        Task<ResponseVM> DeleteShopSettingsAsync(Guid id);

        /// <summary>
        /// إدراج إعدادات المتجر مع ملف
        /// </summary>
        Task<ResponseVM> InsertShopSettingsWithFileAsync(ShopSettingsDTO shopSettings, IBrowserFile file);

        /// <summary>
        /// تحديث إعدادات المتجر مع ملف
        /// </summary>
        Task<ResponseVM> UpdateShopSettingsWithFileAsync(Guid id, ShopSettingsDTO shopSettings, IBrowserFile file);

        /// <summary>
        /// إعادة تعيين إعدادات المتجر للقيم الافتراضية
        /// </summary>
        Task<ResponseVM> ResetShopSettingsAsync();

        /// <summary>
        /// جلب إعدادات المتجر الافتراضية
        /// </summary>
        Task<(ShopSettingsDTO? model, ResponseVM? response)> GetDefaultShopSettingsAsync();
    }
}

using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Receipts;

namespace PosGTech.Web.Services.Interfaces
{
    public interface ISellApiRepository
    {
        /// <summary>
        /// جلب جميع المبيعات
        /// </summary>
        Task<(IEnumerable<SellDTO>? list, ResponseVM? response)> GetAllSellsAsync();

        /// <summary>
        /// جلب جميع المبيعات للكومبو بوكس
        /// </summary>
        Task<(IEnumerable<SellCMDTO>? list, ResponseVM? response)> GetAllSellsCMBAsync();

        /// <summary>
        /// جلب مبيعة بالمعرف
        /// </summary>
        Task<(SellDTO? model, ResponseVM? response)> GetSellByIdAsync(Guid id);

        /// <summary>
        /// جلب مبيعة برقم الفاتورة
        /// </summary>
        Task<(SellReceiptDTO? model, ResponseVM? response)> GetSellByNumAsync(int invoiceNo);

        /// <summary>
        /// إضافة مبيعة جديدة
        /// </summary>
        Task<ResponseVM> InsertSellAsync(SellDTO sell);

        /// <summary>
        /// تحديث مبيعة موجودة
        /// </summary>
        Task<ResponseVM> UpdateSellAsync(Guid id, SellDTO sell);

        /// <summary>
        /// حذف مبيعة
        /// </summary>
        Task<ResponseVM> DeleteSellAsync(Guid id);

        /// <summary>
        /// جلب أرقام المبيعات
        /// </summary>
        Task<(IEnumerable<SellsNumDTO>? list, ResponseVM? response)> GetSellsNumAsync();

        /// <summary>
        /// جلب تقرير المبيعات
        /// </summary>
        Task<(IEnumerable<SalesReportDTO>? list, ResponseVM? response)> GetSalesReportAsync();

        /// <summary>
        /// جلب تقرير المبيعات مع المعاملات
        /// </summary>
        Task<(SalesReportDTO? model, ResponseVM? response)> GetSalesReportAsync(DateOnly fromDate, DateOnly toDate, Guid? storeId = null);
    }
}

﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Items
{
    public partial class ListItems
    {
        [Inject]
        IDialogService DialogService { get; set; }
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        IEnumerable<ItemDTO> items = new List<ItemDTO>();
        IEnumerable<StoreCMDTO> stores = new List<StoreCMDTO>();
        ItemDTO selectedItem = null;
        StoreCMDTO selectedStore = null;
        bool loading = true;
        string Search = "";
        string NameItemForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadingStores();
            await LoadingData();
        }

        private bool FilterFunc1(ItemDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(ItemDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            if (element.Quantity.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            if (element.CostPrice.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertItem>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.ExtraLarge, FullWidth = true };
            var result = await DialogService.Show<UpsertItem>(id != Guid.Empty ? "تعديل صنف" : "إضافة صنف", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(ItemDTO obj)
        {
            NameItemForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Item.DeleteItemAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingStores()
        {
            var res = await _unitOfWork.Store.GetAllStoresCMBAsync();
            if (res.response == null) stores = res.list ?? new List<StoreCMDTO>();
            else _snackbar.Add(res.response.Message, Severity.Error);
        }

        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();

            // Note: The endpoint "getAllItemsByStore" is not available in the current ItemApiRepository
            // Using the general GetAllItemsAsync method instead
            // This may need to be updated when store-specific item filtering is implemented
            var res = await _unitOfWork.Item.GetAllItemsAsync();
            if (res.response == null) items = res.list ?? new List<ItemDTO>();
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }

        async Task OnStoreSelectionChanged(StoreCMDTO store)
        {
            selectedStore = store;
            await LoadingData();
        }

        private async Task<IEnumerable<StoreCMDTO>> SearchStores(string value, CancellationToken token)
        {
            // إضافة خيار "عرض الكل" في بداية القائمة
            var allStoresOption = new List<StoreCMDTO>();

            if (string.IsNullOrEmpty(value) || "عرض الكل".Contains(value, StringComparison.OrdinalIgnoreCase))
            {
                allStoresOption.Add(new StoreCMDTO { Id = Guid.Empty, Name = "عرض الكل" });
            }

            // تصفية المخازن حسب النص المدخل
            var filteredStores = string.IsNullOrEmpty(value)
                ? stores
                : stores.Where(x => x.Name.Contains(value, StringComparison.OrdinalIgnoreCase));

            return await Task.FromResult(allStoresOption.Concat(filteredStores));
        }
    }
}
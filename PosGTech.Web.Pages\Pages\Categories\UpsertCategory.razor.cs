﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Categories;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Categories
{
    public partial class UpsertCategory
    {

        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        CategoryDTO category = new();
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _unitOfWork.Category.GetCategoryByIdAsync(id);
                if (res.response == null)
                {
                    category = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }
            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _unitOfWork.Category.InsertCategoryAsync(category);
            else
                response = await _unitOfWork.Category.UpdateCategoryAsync(id, category);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(category.Name);
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

        private bool isClosing = false;
        private bool isAnimating = false;

        //private async Task CancelWithAnimation()
        //{
        //    isClosing = true;
        //    isAnimating = true;
        //    StateHasChanged();
        //    // ننتظر لإكمال التحريك
        //    await Task.Delay(500);
        //    MudDialog.Cancel();
        //}



    }
}

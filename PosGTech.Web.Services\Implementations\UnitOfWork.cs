using Microsoft.Extensions.Logging;
using PosGTech.Web.Services.Interfaces;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly HttpClient _httpClient;
        private readonly ILocalStorageService _localStorage;
        private readonly ILogger<UnitOfWork> _logger;

        // الوحدات الموجودة حالياً
        public ICategoryApiRepository Category { get; private set; }
        public IClientApiRepository Client { get; private set; }
        public IStoreApiRepository Store { get; private set; }
        public IItemApiRepository Item { get; private set; }
        public IExpenseApiRepository Expense { get; private set; }
        public IEmployeeApiRepository Employee { get; private set; }

        // الوحدات الأساسية الجديدة
        public IPurchaseApiRepository Purchase { get; private set; }
        public ISellApiRepository Sell { get; private set; }
        public IReceiptApiRepository Receipt { get; private set; }
        public IFinancialApiRepository Financial { get; private set; }
        public ITreasuryApiRepository Treasury { get; private set; }
        public IUnitApiRepository Unit { get; private set; }

        // الوحدات المتقدمة الجديدة
        public IInventoryApiRepository Inventory { get; private set; }
        public IConsumedApiRepository Consumed { get; private set; }
        public IStoreItemApiRepository StoreItem { get; private set; }

        // وحدات الإدارة الجديدة
        public IUserApiRepository User { get; private set; }
        public IRoleApiRepository Role { get; private set; }
        public IShopSettingsApiRepository ShopSettings { get; private set; }

        public UnitOfWork(HttpClient httpClient, ILoggerFactory loggerFactory, ILocalStorageService localStorage)
        {
            _httpClient = httpClient;
            _localStorage = localStorage;
            _logger = loggerFactory.CreateLogger<UnitOfWork>();

            // تهيئة الوحدات الموجودة حالياً
            Category = new CategoryApiRepository(httpClient, loggerFactory.CreateLogger<CategoryApiRepository>(), localStorage);
            Client = new ClientApiRepository(httpClient, loggerFactory.CreateLogger<ClientApiRepository>(), localStorage);
            Store = new StoreApiRepository(httpClient, loggerFactory.CreateLogger<StoreApiRepository>(), localStorage);
            Item = new ItemApiRepository(httpClient, loggerFactory.CreateLogger<ItemApiRepository>(), localStorage);
            Expense = new ExpenseApiRepository(httpClient, loggerFactory.CreateLogger<ExpenseApiRepository>(), localStorage);
            Employee = new EmployeeApiRepository(httpClient, loggerFactory.CreateLogger<EmployeeApiRepository>(), localStorage);

            // تهيئة الوحدات الأساسية الجديدة
            Purchase = new PurchaseApiRepository(httpClient, loggerFactory.CreateLogger<PurchaseApiRepository>(), localStorage);
            Sell = new SellApiRepository(httpClient, loggerFactory.CreateLogger<SellApiRepository>(), localStorage);
            Receipt = new ReceiptApiRepository(httpClient, loggerFactory.CreateLogger<ReceiptApiRepository>(), localStorage);
            Financial = new FinancialApiRepository(httpClient, loggerFactory.CreateLogger<FinancialApiRepository>(), localStorage);
            Treasury = new TreasuryApiRepository(httpClient, loggerFactory.CreateLogger<TreasuryApiRepository>(), localStorage);
            Unit = new UnitApiRepository(httpClient, loggerFactory.CreateLogger<UnitApiRepository>(), localStorage);

            // تهيئة الوحدات المتقدمة الجديدة
            Inventory = new InventoryApiRepository(httpClient, loggerFactory.CreateLogger<InventoryApiRepository>(), localStorage);
            Consumed = new ConsumedApiRepository(httpClient, loggerFactory.CreateLogger<ConsumedApiRepository>(), localStorage);
            StoreItem = new StoreItemApiRepository(httpClient, loggerFactory.CreateLogger<StoreItemApiRepository>(), localStorage);

            // تهيئة وحدات الإدارة الجديدة
            User = new UserApiRepository(httpClient, loggerFactory.CreateLogger<UserApiRepository>(), localStorage);
            Role = new RoleApiRepository(httpClient, loggerFactory.CreateLogger<RoleApiRepository>(), localStorage);
            ShopSettings = new ShopSettingsApiRepository(httpClient, loggerFactory.CreateLogger<ShopSettingsApiRepository>(), localStorage);
        }







        public void Dispose()
        {
            // تنظيف الموارد إذا لزم الأمر
        }
    }
}

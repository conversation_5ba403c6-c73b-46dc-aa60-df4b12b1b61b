using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Stores;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// واجهة مستودع API للمخازن - تحتوي على جميع العمليات المطلوبة للتعامل مع المخازن
    /// </summary>
    public interface IStoreApiRepository
    {
        /// <summary>
        /// جلب جميع المخازن
        /// </summary>
        /// <returns>قائمة المخازن مع حالة الاستجابة</returns>
        Task<(IEnumerable<StoreDTO>? list, ResponseVM? response)> GetAllStoresAsync();

        /// <summary>
        /// جلب جميع المخازن للقوائم المنسدلة
        /// </summary>
        /// <returns>قائمة المخازن المبسطة مع حالة الاستجابة</returns>
        Task<(IEnumerable<StoreCMDTO>? list, ResponseVM? response)> GetAllStoresCMBAsync();

        /// <summary>
        /// جلب مخزن بالمعرف
        /// </summary>
        /// <param name="id">معرف المخزن</param>
        /// <returns>بيانات المخزن مع حالة الاستجابة</returns>
        Task<(StoreDTO? model, ResponseVM? response)> GetStoreByIdAsync(Guid id);

        /// <summary>
        /// إضافة مخزن جديد
        /// </summary>
        /// <param name="store">بيانات المخزن الجديد</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> InsertStoreAsync(StoreDTO store);

        /// <summary>
        /// تحديث مخزن موجود
        /// </summary>
        /// <param name="id">معرف المخزن</param>
        /// <param name="store">بيانات المخزن المحدثة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> UpdateStoreAsync(Guid id, StoreDTO store);

        /// <summary>
        /// حذف مخزن
        /// </summary>
        /// <param name="id">معرف المخزن</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> DeleteStoreAsync(Guid id);

        /// <summary>
        /// جلب جميع إعدادات المخازن
        /// </summary>
        /// <returns>قائمة إعدادات المخازن مع حالة الاستجابة</returns>
        Task<(IEnumerable<StoreSettingsDTO>? list, ResponseVM? response)> GetAllStoreSettingsAsync();

        /// <summary>
        /// جلب إعدادات مخزن بالمعرف
        /// </summary>
        /// <param name="id">معرف المخزن</param>
        /// <returns>إعدادات المخزن مع حالة الاستجابة</returns>
        Task<(StoreSettingsDTO? model, ResponseVM? response)> GetStoreSettingsByIdAsync(Guid id);

        /// <summary>
        /// إضافة إعدادات مخزن جديدة
        /// </summary>
        /// <param name="storeSettings">إعدادات المخزن الجديدة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> InsertStoreSettingsAsync(StoreSettingsDTO storeSettings);

        /// <summary>
        /// تحديث إعدادات مخزن موجودة
        /// </summary>
        /// <param name="id">معرف المخزن</param>
        /// <param name="storeSettings">إعدادات المخزن المحدثة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> UpdateStoreSettingsAsync(Guid id, StoreSettingsDTO storeSettings);
    }
}

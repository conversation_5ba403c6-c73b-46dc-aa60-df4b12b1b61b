﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using MudExtensions;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Authorization;
using PosGTech.ModelsDTO.Roles;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text.Json;

namespace PosGTech.Web.Pages.Pages.Roles;

public partial class UpsertRole
{
    [Parameter] public Guid id { get; set; }

    private RoleDTO roleModel = new();
    private List<BreadcrumbItem> _items = new();
    private string pageTitle = "";
    private bool isLoading = false;
    private bool showPresetRoles = false;

    private bool IsEdit => id != Guid.Empty;
    private bool IsSystemRole => IsEdit && IsSystemRoleByName(roleModel.Name);

    [Inject] IUnitOfWork _unitOfWork { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (IsEdit)
        {
            await LoadRole();
            pageTitle = "تعديل الدور";
        }
        else
        {
            roleModel = new RoleDTO();
            pageTitle = "إضافة دور جديد";
        }

        _items = new List<BreadcrumbItem>
    {
        new BreadcrumbItem("الرئيسية", href: "", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("إدارة الأدوار", href: "/listRoles", icon: Icons.Material.Filled.AdminPanelSettings),
        new BreadcrumbItem(pageTitle, href: null, disabled: true, icon: Icons.Material.Filled.Security)
    };
    }

    private async Task LoadRole()
    {
        try
        {
            var response = await _unitOfWork.Role.GetRoleByIdAsync(id.ToString());
            if (response.response == null)
            {
                roleModel = response.model;
            }
            else
            {
                _snackbar.Add("خطأ في تحميل بيانات الدور", Severity.Error);
                _navigation.NavigateTo("/listRoles");
            }
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في تحميل بيانات الدور: {ex.Message}", Severity.Error);
            _navigation.NavigateTo("/listRoles");
        }
    }

    private async Task SaveRole()
    {
        if (roleModel.Permissions?.Count == 0)
        {
            _snackbar.Add("يجب اختيار صلاحية واحدة على الأقل", Severity.Warning);
            return;
        }

        isLoading = true;
        try
        {
            ResponseVM response;

            if (IsEdit)
            {
                // تحديث الدور الموجود - استخدام النمط المعياري
                roleModel.Id = id;
                response = await _unitOfWork.Role.UpdateRoleAsync(id.ToString(), roleModel);
            }
            else
            {
                // إنشاء دور جديد - استخدام النمط المعياري
                response = await _unitOfWork.Role.InsertRoleAsync(roleModel);
            }

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                _navigation.NavigateTo("/listRoles");
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في حفظ الدور: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }



    private void SelectAllPermissions()
    {
        roleModel.Permissions = PermissionConstants.GetAllPermissions().ToList();
        StateHasChanged();
    }

    private void ClearAllPermissions()
    {
        roleModel.Permissions = new List<string>();
        StateHasChanged();
    }

    private void ApplyRolePreset(string presetRole)
    {
        roleModel.Permissions = GetRolePresetPermissions(presetRole);
        showPresetRoles = false;
        StateHasChanged();
    }

    private List<string> GetRolePresetPermissions(string roleName)
    {
        // هذه قوالب افتراضية للأدوار - يمكن تخصيصها حسب الحاجة
        return roleName switch
        {
            "SalesEmployee" => new List<string>
        {
            "Sales.View", "Sales.Add", "Sales.Edit",
            "Clients.View", "Clients.Add", "Clients.Edit",
            "Items.View", "Reports.Sales"
        },
            "PurchaseManager" => new List<string>
        {
            "Purchase.View", "Purchase.Add", "Purchase.Edit", "Purchase.Delete",
            "Suppliers.View", "Suppliers.Add", "Suppliers.Edit",
            "Items.View", "Items.Add", "Items.Edit",
            "Reports.Purchase"
        },
            "Accountant" => new List<string>
        {
            "Treasury.View", "Treasury.Add", "Treasury.Edit",
            "Reports.Financial", "Reports.Treasury",
            "Sales.View", "Purchase.View"
        },
            "InventoryManager" => new List<string>
        {
            "Inventory.View", "Inventory.Add", "Inventory.Edit",
            "Items.View", "Items.Add", "Items.Edit", "Items.Delete",
            "StoreItems.View", "StoreItems.Add", "StoreItems.Edit",
            "Reports.Inventory"
        },
            _ => new List<string>()
        };
    }

    private bool IsSystemRoleByName(string roleName)
    {
        var systemRoles = new[] { "SystemAdmin", "SalesEmployee", "PurchaseManager", "Accountant", "InventoryManager" };
        return systemRoles.Contains(roleName);
    }
}
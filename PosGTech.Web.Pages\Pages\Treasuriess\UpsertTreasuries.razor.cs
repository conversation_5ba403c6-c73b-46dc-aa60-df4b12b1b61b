﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Treasuriess
{
    public partial class UpsertTreasuries
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        TreasuryDTO Treasury
            = new();
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _unitOfWork.Treasury.GetTreasuryByIdAsync(id);
                if (res.response == null)
                {
                    Treasury = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }
            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _unitOfWork.Treasury.InsertTreasuryAsync(Treasury);
            else
                response = await _unitOfWork.Treasury.UpdateTreasuryAsync(id, Treasury);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(Treasury.Name);
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

        private bool isClosing = false;
        private bool isAnimating = false;


    }
}
﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Units;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Units
{
    public partial class ListUnits
    {
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        IEnumerable<UnitDTO> Units = new List<UnitDTO>();
        [Inject]
        IDialogService DialogService { get; set; }
        UnitDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameUnitForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();
        private bool FilterFunc1(UnitDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(UnitDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertUnit>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertUnit>(id != Guid.Empty ? "تعديل وحدة" : "إضافة وحدة", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(UnitDTO obj)
        {
            NameUnitForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Unit.DeleteUnitAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _unitOfWork.Unit.GetAllUnitsAsync();
            if (res.response == null) Units = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}
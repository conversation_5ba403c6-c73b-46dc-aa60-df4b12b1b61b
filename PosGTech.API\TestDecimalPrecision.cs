using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.Models;

namespace PosGTech.API
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestDecimalPrecisionController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public TestDecimalPrecisionController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpPost("test-purchase-item-precision")]
        public async Task<IActionResult> TestPurchaseItemPrecision([FromBody] TestDecimalRequest request)
        {
            try
            {
                // إنشاء عنصر شراء جديد للاختبار
                var purchaseItem = new PurchaseItem
                {
                    Id = Guid.NewGuid(),
                    Price = request.TestValue,
                    PriceAfterDiscount = request.TestValue,
                    Quantity = request.TestValue,
                    ReturnQuantity = 0,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.PurchaseItems.Add(purchaseItem);
                await _context.SaveChangesAsync();

                // استرجاع العنصر من قاعدة البيانات
                var retrievedItem = await _context.PurchaseItems
                    .FirstOrDefaultAsync(p => p.Id == purchaseItem.Id);

                if (retrievedItem == null)
                {
                    return NotFound("لم يتم العثور على العنصر");
                }

                var result = new
                {
                    OriginalValue = request.TestValue,
                    StoredPrice = retrievedItem.Price,
                    StoredPriceAfterDiscount = retrievedItem.PriceAfterDiscount,
                    StoredQuantity = retrievedItem.Quantity,
                    PrecisionPreserved = retrievedItem.Price == request.TestValue &&
                                       retrievedItem.PriceAfterDiscount == request.TestValue &&
                                       retrievedItem.Quantity == request.TestValue,
                    Message = retrievedItem.Price == request.TestValue ? 
                             "تم الحفاظ على دقة الأعداد العشرية بنجاح!" : 
                             "فقدان في دقة الأعداد العشرية!"
                };

                // حذف العنصر التجريبي
                _context.PurchaseItems.Remove(retrievedItem);
                await _context.SaveChangesAsync();

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في الاختبار: {ex.Message}");
            }
        }

        [HttpPost("test-multiple-values")]
        public async Task<IActionResult> TestMultipleValues()
        {
            var testValues = new decimal[] { 1.75m, 2.125m, 3.999m, 0.001m, 123.456m };
            var results = new List<object>();

            foreach (var testValue in testValues)
            {
                try
                {
                    var purchaseItem = new PurchaseItem
                    {
                        Id = Guid.NewGuid(),
                        Price = testValue,
                        PriceAfterDiscount = testValue,
                        Quantity = testValue,
                        ReturnQuantity = 0,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    _context.PurchaseItems.Add(purchaseItem);
                    await _context.SaveChangesAsync();

                    var retrievedItem = await _context.PurchaseItems
                        .FirstOrDefaultAsync(p => p.Id == purchaseItem.Id);

                    if (retrievedItem != null)
                    {
                        results.Add(new
                        {
                            TestValue = testValue,
                            StoredValue = retrievedItem.Price,
                            PrecisionPreserved = retrievedItem.Price == testValue,
                            Difference = retrievedItem.Price - testValue
                        });

                        _context.PurchaseItems.Remove(retrievedItem);
                        await _context.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        TestValue = testValue,
                        Error = ex.Message
                    });
                }
            }

            return Ok(new
            {
                TestResults = results,
                Summary = new
                {
                    TotalTests = testValues.Length,
                    PassedTests = results.Count(r => r.GetType().GetProperty("PrecisionPreserved")?.GetValue(r) as bool? == true),
                    FailedTests = results.Count(r => r.GetType().GetProperty("PrecisionPreserved")?.GetValue(r) as bool? == false)
                }
            });
        }
    }

    public class TestDecimalRequest
    {
        public decimal TestValue { get; set; }
    }
}

﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PosGTech.Models;
using PosGTech.ModelsDTO.Authorization;
using PosGTech.ModelsDTO.Finacnial;
using System.Linq;
using System.Security.Claims;

namespace PosGTech.DataAccess.Data;

/// <summary>
/// فئة تهيئة البيانات الأساسية للنظام
/// تحتوي على البيانات الأساسية فقط (Financials, Treasuries, Default Client)
/// الأدوار والمستخدمين سيتم إضافتهم في النظام الجديد
/// </summary>
public class SeedData(ApplicationDbContext _db, UserManager<User> user, RoleManager<Role> role)
{
    public async Task init()
    {
        try
        {
            // زيادة المهلة الزمنية لجميع العمليات
            _db.Database.SetCommandTimeout(600); // 10 دقائق

            Console.WriteLine("بدء تهيئة النظام...");

            // تطبيق المايجريشن
            Console.WriteLine("تطبيق المايجريشن...");
            _db.Database.Migrate();
            Console.WriteLine("تم تطبيق المايجريشن بنجاح.");

            // التحقق من عدم وجود بيانات مسبقة
            Console.WriteLine("فحص البيانات المالية الأساسية...");
            if (!_db.Financials.Any())
            {
                Console.WriteLine("إضافة البيانات المالية الأساسية...");
                // إضافة البيانات المالية الأساسية
                await SeedFinancialData();

                // إضافة الخزائن الأساسية
                await SeedTreasuryData();

                // إضافة العميل النقدي الافتراضي
                await SeedDefaultClient();

                // حفظ البيانات الأساسية
                await _db.SaveChangesAsync();
                Console.WriteLine("تم حفظ البيانات المالية الأساسية بنجاح.");
            }

            // التحقق من عدم وجود أدوار مسبقة
            if (!await role.RoleExistsAsync("مدير النظام"))
                // التحقق من عدم وجود أدوار مسبقة بطريقة محسنة
                Console.WriteLine("فحص الأدوار الافتراضية...");
            var adminRoleExists = await CheckRoleExistsWithTimeout("مدير النظام");
            if (!adminRoleExists)
            {
                Console.WriteLine("إنشاء الأدوار الافتراضية...");
                // إنشاء الأدوار الافتراضية مع صلاحياتها
                await SeedDefaultRoles();

                // إنشاء المستخدم الافتراضي
                await SeedDefaultUser();
                Console.WriteLine("تم إنشاء الأدوار والمستخدم الافتراضي بنجاح.");
            }

            // إضافة البيانات الأولية الشاملة
            Console.WriteLine("بدء إضافة البيانات الأولية الشاملة...");
            await SeedComprehensiveData();
            Console.WriteLine("تم الانتهاء من تهيئة النظام بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في تهيئة النظام: {ex.Message}");
            Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            throw;
        }
    }

    /// <summary>
    /// فحص وجود الدور مع مهلة زمنية محسنة
    /// </summary>
    private async Task<bool> CheckRoleExistsWithTimeout(string roleName)
    {
        try
        {
            Console.WriteLine($"فحص وجود الدور: {roleName}");

            // استخدام استعلام مباشر بدلاً من RoleExistsAsync
            var roleCount = await _db.Roles.CountAsync(r => r.Name == roleName);
            var exists = roleCount > 0;

            Console.WriteLine($"الدور {roleName} موجود: {exists}");
            return exists;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في فحص الدور {roleName}: {ex.Message}");
            // في حالة الخطأ، نفترض أن الدور غير موجود
            return false;
        }
    }

    /// <summary>
    /// إضافة البيانات المالية الأساسية
    /// </summary>
    private async Task SeedFinancialData()
    {
        var financials = new List<Financial>
        {
            new() { Id = FinancialId.Client, Name = "عميل" },
            new() { Id = FinancialId.Deposit, Name = "إيداع" },
            new() { Id = FinancialId.Withdrawal, Name = "سحب" },
            new() { Id = FinancialId.Transfer, Name = "تحويل" },
            new() { Id = FinancialId.SalaryPayment, Name = "صرف مرتب" },
            new() { Id = FinancialId.OpeningBalanceForClient, Name = "رصيد افتتاحي للعميل" },
            new() { Id = FinancialId.OpeningBalanceForEmployee, Name = "رصيد افتتاحي للموظف" },
            new() { Id = FinancialId.Employee, Name = "موظف" },
            new() { Id = FinancialId.Sale, Name = "فاتورة مبيعات" },
            new() { Id = FinancialId.Purchase, Name = "فاتورة مشتريات" },
            new() { Id = FinancialId.Expense, Name = "مصروفات" }
        };

        _db.Financials.AddRange(financials);
    }

    /// <summary>
    /// إضافة الخزائن الأساسية
    /// </summary>
    private async Task SeedTreasuryData()
    {
        var treasuries = new List<Treasury>
        {
            new() { Name = "النقدية" },
            new() { Name = "بطاقة مصرفية" }
        };

        _db.Treasuries.AddRange(treasuries);
    }

    /// <summary>
    /// إضافة العميل النقدي الافتراضي
    /// </summary>
    private async Task SeedDefaultClient()
    {
        var cashClient = new Client
        {
            Id = new Guid("4BDB0B09-291E-4C0E-8792-250E2A37B13D"),
            Name = "زبون نقدي",
            IsSupplier = false,
            Balance = 0,
            IsCustomer = true,
            Address = ""
        };

        _db.Clients.Add(cashClient);
    }

    /// <summary>
    /// إنشاء الأدوار الافتراضية مع صلاحياتها
    /// </summary>
    private async Task SeedDefaultRoles()
    {
        foreach (var (name, permissions) in GetDefaultRoles())
        {
            // إنشاء الدور
            var newRole = new Role
            {
                Id = Guid.NewGuid(),
                Name = name
            };

            var result = await role.CreateAsync(newRole);

            if (result.Succeeded)
            {
                // إضافة الصلاحيات كـ Claims للدور
                foreach (var permission in permissions)
                {
                    await role.AddClaimAsync(newRole, new Claim("permission", permission));
                }
            }
        }
    }

    /// <summary>
    /// الحصول على الأدوار الافتراضية وصلاحياتها
    /// هذه الأدوار يتم إنشاؤها تلقائياً عند أول تشغيل للنظام
    /// ويمكن تعديلها لاحقاً من واجهة إدارة النظام
    /// </summary>
    /// <returns>قائمة الأدوار الافتراضية مع أسمائها وأوصافها وصلاحياتها</returns>
    private static List<(string Name, List<string> Permissions)> GetDefaultRoles()
    {
        return new List<(string Name, List<string> Permissions)>
        {
            (
                "مدير النظام",
                PermissionConstants.GetAllPermissions()
            ),
            (
                "موظف المبيعات",
                new List<string>
                {
                    // Sales permissions
                    PermissionConstants.SalesView,
                    PermissionConstants.SalesAdd,
                    PermissionConstants.SalesEdit,

                    // Clients permissions (customers)
                    PermissionConstants.ClientsView,
                    PermissionConstants.ClientsAdd,
                    PermissionConstants.ClientsEdit,

                    // Items view only (for sales)
                    PermissionConstants.ItemsView,
                    PermissionConstants.CategoriesView,
                    PermissionConstants.UnitsView,

                    // Basic treasury operations
                    PermissionConstants.TreasuriesView,

                    // Receipts for sales
                    PermissionConstants.ReceiptsView,
                    PermissionConstants.ReceiptsAdd,
                    PermissionConstants.ReceiptsEdit,

                    // Reports
                    PermissionConstants.ReportsView,
                    PermissionConstants.ReportsGenerate,
                    PermissionConstants.ReportsExport
                }
            ),
            (
                "مسؤول المشتريات",
                new List<string>
                {
                    // Purchase permissions
                    PermissionConstants.PurchasesView,
                    PermissionConstants.PurchasesAdd,
                    PermissionConstants.PurchasesEdit,
                    PermissionConstants.PurchasesDelete,

                    // Suppliers (Clients as suppliers)
                    PermissionConstants.ClientsView,
                    PermissionConstants.ClientsAdd,
                    PermissionConstants.ClientsEdit,
                    PermissionConstants.ClientsDelete,

                    // Inventory management
                    PermissionConstants.InventoryView,
                    PermissionConstants.InventoryAdd,
                    PermissionConstants.InventoryEdit,
                    PermissionConstants.InventoryDelete,

                    // Items management
                    PermissionConstants.ItemsView,
                    PermissionConstants.ItemsAdd,
                    PermissionConstants.ItemsEdit,
                    PermissionConstants.ItemsDelete,
                    PermissionConstants.CategoriesView,
                    PermissionConstants.CategoriesAdd,
                    PermissionConstants.CategoriesEdit,
                    PermissionConstants.UnitsView,
                    PermissionConstants.UnitsAdd,
                    PermissionConstants.UnitsEdit,

                    // Stores management
                    PermissionConstants.StoresView,
                    PermissionConstants.StoresAdd,
                    PermissionConstants.StoresEdit,

                    // Receipts for purchases
                    PermissionConstants.ReceiptsView,
                    PermissionConstants.ReceiptsAdd,
                    PermissionConstants.ReceiptsEdit,

                    // Reports
                    PermissionConstants.ReportsView,
                    PermissionConstants.ReportsGenerate,
                    PermissionConstants.ReportsExport
                }
            ),
            (
                "محاسب",
                new List<string>
                {
                    // Financial reports (full access)
                    PermissionConstants.ReportsView,
                    PermissionConstants.ReportsGenerate,
                    PermissionConstants.ReportsExport,
                    PermissionConstants.ReportsAdvanced,

                    // Treasury management
                    PermissionConstants.TreasuriesView,
                    PermissionConstants.TreasuriesAdd,
                    PermissionConstants.TreasuriesEdit,

                    // Receipts management
                    PermissionConstants.ReceiptsView,
                    PermissionConstants.ReceiptsAdd,
                    PermissionConstants.ReceiptsEdit,
                    PermissionConstants.ReceiptsDelete,

                    // Expenses management
                    PermissionConstants.ExpensesView,
                    PermissionConstants.ExpensesAdd,
                    PermissionConstants.ExpensesEdit,
                    PermissionConstants.ExpensesDelete,

                    // View sales and purchases for financial tracking
                    PermissionConstants.SalesView,
                    PermissionConstants.PurchasesView,

                    // Clients for financial tracking
                    PermissionConstants.ClientsView,
                    PermissionConstants.ClientsEdit,

                    // Employees for salary management
                    PermissionConstants.EmployeesView
                }
            ),
            (
                "مدير المخزون",
                new List<string>
                {
                    // Full inventory management
                    PermissionConstants.InventoryView,
                    PermissionConstants.InventoryAdd,
                    PermissionConstants.InventoryEdit,
                    PermissionConstants.InventoryDelete,

                    // Items management
                    PermissionConstants.ItemsView,
                    PermissionConstants.ItemsAdd,
                    PermissionConstants.ItemsEdit,
                    PermissionConstants.ItemsDelete,

                    // Categories and Units management
                    PermissionConstants.CategoriesView,
                    PermissionConstants.CategoriesAdd,
                    PermissionConstants.CategoriesEdit,
                    PermissionConstants.CategoriesDelete,
                    PermissionConstants.UnitsView,
                    PermissionConstants.UnitsAdd,
                    PermissionConstants.UnitsEdit,
                    PermissionConstants.UnitsDelete,

                    // Stores management
                    PermissionConstants.StoresView,
                    PermissionConstants.StoresAdd,
                    PermissionConstants.StoresEdit,
                    PermissionConstants.StoresDelete,

                    // View purchases for inventory tracking
                    PermissionConstants.PurchasesView,

                    // View sales for inventory tracking
                    PermissionConstants.SalesView,

                    // Inventory reports
                    PermissionConstants.ReportsView,
                    PermissionConstants.ReportsGenerate,
                    PermissionConstants.ReportsExport,

                    // Basic client view for inventory purposes
                    PermissionConstants.ClientsView
                }
            )
        };
    }

    /// <summary>
    /// إنشاء المستخدم الافتراضي (مدير النظام)
    /// </summary>
    private async Task SeedDefaultUser()
    {
        // التأكد من وجود الخزائن قبل إنشاء المستخدم
        var treasuries = await _db.Treasuries.ToListAsync();

        var adminUser = new User
        {
            Id = Guid.NewGuid(),
            Name = "مدير النظام",
            UserName = "Admin",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            UserTreasuries = treasuries.Select(t => new UserTreasury
            {
                TreasuryId = t.Id,
                Balance = 0
            }).ToList()
        };

        var result = await user.CreateAsync(adminUser, "Admin123*");

        if (result.Succeeded)
        {
            // إضافة دور مدير النظام للمستخدم
            await user.AddToRoleAsync(adminUser, "مدير النظام");
           await user.AddClaimsAsync(adminUser, PermissionConstants.GetAllPermissions().Select(x=>new Claim("permission",x)));
            await user.AddClaimsAsync(adminUser, PermissionConstants.GetAllPermissions().Select(x => new Claim("permission", x)));
        }
    }

    /// <summary>
    /// إضافة البيانات الأولية الشاملة للنظام
    /// </summary>
    private async Task SeedComprehensiveData()
    {
        try
        {
            // زيادة المهلة الزمنية لقاعدة البيانات
            _db.Database.SetCommandTimeout(300); // 5 دقائق

            // إضافة وحدات القياس إذا لم تكن موجودة
            if (!_db.Units.Any())
            {
                Console.WriteLine("إضافة وحدات القياس...");
                await SeedUnits();
                await _db.SaveChangesAsync();
                Console.WriteLine("تم حفظ وحدات القياس بنجاح.");
            }

            // إضافة التصنيفات إذا لم تكن موجودة
            if (!_db.Categories.Any())
            {
                Console.WriteLine("إضافة التصنيفات...");
                await SeedCategories();
                await _db.SaveChangesAsync();
                Console.WriteLine("تم حفظ التصنيفات بنجاح.");
            }

            // إضافة المخازن إذا لم تكن موجودة
            if (!_db.Stores.Any())
            {
                Console.WriteLine("إضافة المخازن...");
                await SeedStores();
                await _db.SaveChangesAsync();
                Console.WriteLine("تم حفظ المخازن بنجاح.");
            }

            // إضافة العملاء والموردين إذا لم يكونوا موجودين (باستثناء العميل النقدي الافتراضي)
            var clientsCount = await _db.Clients.CountAsync();
            if (clientsCount <= 1) // فقط العميل النقدي الافتراضي
            {
                Console.WriteLine("إضافة العملاء والموردين بشكل تدريجي...");
                await SeedClientsAndSuppliersInBatches();
                Console.WriteLine("تم حفظ العملاء والموردين بنجاح.");
            }

            // إضافة الأصناف مع وحداتها إذا لم تكن موجودة (بشكل تدريجي)
            if (!_db.Items.Any())
            {
                Console.WriteLine("إضافة الأصناف مع وحداتها...");
                await SeedItemsWithUnitsInBatches();
                Console.WriteLine("تم حفظ جميع الأصناف بنجاح.");
            }

            // إضافة المستخدمين الإضافيين (بعد التأكد من وجود الأدوار)
            await SeedAdditionalUsers();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة البيانات الأولية: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// إضافة الأصناف مع وحداتها بشكل تدريجي لتجنب مشاكل المهلة الزمنية
    /// </summary>
    private async Task SeedItemsWithUnitsInBatches()
    {
        try
        {
            Console.WriteLine("بدء إضافة الأصناف بشكل تدريجي...");

            // الحصول على البيانات المرجعية
            var categories = await _db.Categories.ToListAsync();
            var units = await _db.Units.ToListAsync();

            Console.WriteLine($"تم العثور على {categories.Count} تصنيف و {units.Count} وحدة قياس");

            if (!categories.Any() || !units.Any())
            {
                Console.WriteLine("خطأ: لا توجد تصنيفات أو وحدات قياس!");
                return;
            }

            // تجميع الوحدات حسب النوع لسهولة الوصول
            var pieceUnit = units.FirstOrDefault(u => u.Name == "قطعة");
            var kiloUnit = units.FirstOrDefault(u => u.Name == "كيلو");
            var gramUnit = units.FirstOrDefault(u => u.Name == "جرام");
            var literUnit = units.FirstOrDefault(u => u.Name == "لتر");
            var mlUnit = units.FirstOrDefault(u => u.Name == "مليلتر");
            var dozenUnit = units.FirstOrDefault(u => u.Name == "دستة");
            var cartonUnit = units.FirstOrDefault(u => u.Name == "كرتون");
            var boxUnit = units.FirstOrDefault(u => u.Name == "علبة");
            var bagUnit = units.FirstOrDefault(u => u.Name == "كيس");
            var bottleUnit = units.FirstOrDefault(u => u.Name == "زجاجة");

            // تجميع التصنيفات حسب النوع
            var foodCategory = categories.FirstOrDefault(c => c.Name == "مواد غذائية");
            var drinksCategory = categories.FirstOrDefault(c => c.Name == "مشروبات");
            var dairyCategory = categories.FirstOrDefault(c => c.Name == "منتجات الألبان");
            var meatCategory = categories.FirstOrDefault(c => c.Name == "لحوم ودواجن");
            var vegetablesCategory = categories.FirstOrDefault(c => c.Name == "خضروات وفواكه");

            // إضافة جميع مجموعات الأصناف للوصول إلى 500 صنف
            await SeedAndSaveFoodItems(foodCategory, pieceUnit, kiloUnit, gramUnit, bagUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveDrinkItems(drinksCategory, bottleUnit, literUnit, mlUnit, cartonUnit, dozenUnit, pieceUnit, boxUnit);
            await SeedAndSaveDairyItems(dairyCategory, literUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, mlUnit);
            await SeedAndSaveMeatItems(meatCategory, kiloUnit, gramUnit, pieceUnit, bagUnit);
            await SeedAndSaveVegetableItems(vegetablesCategory, kiloUnit, gramUnit, pieceUnit, bagUnit);

            // إضافة باقي التصنيفات
            var cleaningCategory = categories.FirstOrDefault(c => c.Name == "مواد تنظيف");
            var householdCategory = categories.FirstOrDefault(c => c.Name == "أدوات منزلية");
            var personalCategory = categories.FirstOrDefault(c => c.Name == "مستلزمات شخصية");
            var medicineCategory = categories.FirstOrDefault(c => c.Name == "أدوية ومستلزمات طبية");
            var stationeryCategory = categories.FirstOrDefault(c => c.Name == "قرطاسية ومكتبية");
            var clothingCategory = categories.FirstOrDefault(c => c.Name == "ملابس وأحذية");
            var electronicsCategory = categories.FirstOrDefault(c => c.Name == "إلكترونيات");
            var sportsCategory = categories.FirstOrDefault(c => c.Name == "أدوات رياضية");
            var toysCategory = categories.FirstOrDefault(c => c.Name == "ألعاب أطفال");
            var miscCategory = categories.FirstOrDefault(c => c.Name == "متنوعات");

            await SeedAndSaveCleaningItems(cleaningCategory, literUnit, mlUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, dozenUnit);
            await SeedAndSaveHouseholdItems(householdCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSavePersonalItems(personalCategory, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);
            await SeedAndSaveMedicineItems(medicineCategory, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);
            await SeedAndSaveStationeryItems(stationeryCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveClothingItems(clothingCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveElectronicsItems(electronicsCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveSportsItems(sportsCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveToysItems(toysCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);
            await SeedAndSaveMiscItems(miscCategory, pieceUnit, kiloUnit, gramUnit, literUnit, mlUnit, bottleUnit, boxUnit, bagUnit, cartonUnit, dozenUnit);

            // إضافة أصناف إضافية للوصول إلى 500 صنف
            Console.WriteLine("إضافة أصناف إضافية للوصول إلى 500 صنف...");
            await SeedAdditionalItems(categories, units);

            Console.WriteLine("تم الانتهاء من إضافة جميع الأصناف (500 صنف)!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة الأصناف: {ex.Message}");
            Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            throw;
        }
    }

    /// <summary>
    /// إضافة أصناف المواد الغذائية وحفظها
    /// </summary>
    private async Task SeedAndSaveFoodItems(Category category, Unit pieceUnit, Unit kiloUnit, Unit gramUnit, Unit bagUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف المواد الغذائية...");
        await SeedFoodItems(category, pieceUnit, kiloUnit, gramUnit, bagUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف المواد الغذائية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف المشروبات وحفظها
    /// </summary>
    private async Task SeedAndSaveDrinkItems(Category category, Unit bottleUnit, Unit literUnit, Unit mlUnit, Unit cartonUnit, Unit dozenUnit, Unit pieceUnit, Unit boxUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف المشروبات...");
        await SeedDrinkItems(category, bottleUnit, literUnit, mlUnit, cartonUnit, dozenUnit, pieceUnit, boxUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف المشروبات بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف منتجات الألبان وحفظها
    /// </summary>
    private async Task SeedAndSaveDairyItems(Category category, Unit literUnit, Unit kiloUnit, Unit gramUnit, Unit bottleUnit, Unit boxUnit, Unit pieceUnit, Unit mlUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف منتجات الألبان...");
        await SeedDairyItems(category, literUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, mlUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف منتجات الألبان بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف اللحوم وحفظها
    /// </summary>
    private async Task SeedAndSaveMeatItems(Category category, Unit kiloUnit, Unit gramUnit, Unit pieceUnit, Unit bagUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف اللحوم والدواجن...");
        await SeedMeatItems(category, kiloUnit, gramUnit, pieceUnit, bagUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف اللحوم والدواجن بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الخضروات وحفظها
    /// </summary>
    private async Task SeedAndSaveVegetableItems(Category category, Unit kiloUnit, Unit gramUnit, Unit pieceUnit, Unit bagUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الخضروات والفواكه...");
        await SeedVegetableItems(category, kiloUnit, gramUnit, pieceUnit, bagUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الخضروات والفواكه بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف مواد التنظيف وحفظها
    /// </summary>
    private async Task SeedAndSaveCleaningItems(Category category, Unit literUnit, Unit mlUnit, Unit kiloUnit, Unit gramUnit, Unit bottleUnit, Unit boxUnit, Unit pieceUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف مواد التنظيف...");
        await SeedCleaningItems(category, literUnit, mlUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف مواد التنظيف بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الأدوات المنزلية وحفظها
    /// </summary>
    private async Task SeedAndSaveHouseholdItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الأدوات المنزلية...");
        await SeedHouseholdItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الأدوات المنزلية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف المستلزمات الشخصية وحفظها
    /// </summary>
    private async Task SeedAndSavePersonalItems(Category category, Unit pieceUnit, Unit bottleUnit, Unit boxUnit, Unit gramUnit, Unit mlUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف المستلزمات الشخصية...");
        await SeedPersonalItems(category, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف المستلزمات الشخصية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الأدوية وحفظها
    /// </summary>
    private async Task SeedAndSaveMedicineItems(Category category, Unit pieceUnit, Unit bottleUnit, Unit boxUnit, Unit gramUnit, Unit mlUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الأدوية والمستلزمات الطبية...");
        await SeedMedicineItems(category, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الأدوية والمستلزمات الطبية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف القرطاسية وحفظها
    /// </summary>
    private async Task SeedAndSaveStationeryItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف القرطاسية والمكتبية...");
        await SeedStationeryItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف القرطاسية والمكتبية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الملابس والأحذية وحفظها
    /// </summary>
    private async Task SeedAndSaveClothingItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الملابس والأحذية...");
        await SeedClothingItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الملابس والأحذية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الإلكترونيات وحفظها
    /// </summary>
    private async Task SeedAndSaveElectronicsItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الإلكترونيات...");
        await SeedElectronicsItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الإلكترونيات بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف الأدوات الرياضية وحفظها
    /// </summary>
    private async Task SeedAndSaveSportsItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف الأدوات الرياضية...");
        await SeedSportsItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف الأدوات الرياضية بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف ألعاب الأطفال وحفظها
    /// </summary>
    private async Task SeedAndSaveToysItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف ألعاب الأطفال...");
        await SeedToysItems(category, pieceUnit, boxUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف ألعاب الأطفال بنجاح.");
    }

    /// <summary>
    /// إضافة أصناف متنوعة وحفظها
    /// </summary>
    private async Task SeedAndSaveMiscItems(Category category, Unit pieceUnit, Unit kiloUnit, Unit gramUnit, Unit literUnit, Unit mlUnit, Unit bottleUnit, Unit boxUnit, Unit bagUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        Console.WriteLine("إضافة أصناف متنوعة...");
        await SeedMiscItems(category, pieceUnit, kiloUnit, gramUnit, literUnit, mlUnit, bottleUnit, boxUnit, bagUnit, cartonUnit, dozenUnit);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ أصناف متنوعة بنجاح.");
    }

    /// <summary>
    /// إضافة وحدات القياس الأساسية
    /// </summary>
    private async Task SeedUnits()
    {
        var units = new List<Unit>
        {
            new() { Id = Guid.NewGuid(), Name = "قطعة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "كيلو", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "جرام", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "لتر", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مليلتر", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "متر", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "سنتيمتر", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "دستة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "كرتون", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "علبة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "كيس", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "زجاجة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "عبوة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "باكيت", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "طن", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now }
        };

        _db.Units.AddRange(units);
    }

    /// <summary>
    /// إضافة التصنيفات الشاملة - تصنيفات متنوعة حسب الأصناف
    /// </summary>
    private async Task SeedCategories()
    {
        var categories = new List<Category>
        {
            // التصنيفات الأساسية
            new() { Id = Guid.NewGuid(), Name = "مواد غذائية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مشروبات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "منتجات الألبان", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "لحوم ودواجن", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "خضروات وفواكه", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مواد تنظيف", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات منزلية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مستلزمات شخصية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوية ومستلزمات طبية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "قرطاسية ومكتبية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "ملابس وأحذية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "إلكترونيات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات رياضية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "ألعاب أطفال", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "متنوعات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },

            // تصنيفات فرعية للمواد الغذائية
            new() { Id = Guid.NewGuid(), Name = "حبوب ومعكرونة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "زيوت ودهون", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "بهارات وتوابل", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "معلبات ومحفوظات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "حلويات وشوكولاتة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مخبوزات وكعك", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مكسرات وفواكه مجففة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },

            // تصنيفات فرعية للمشروبات
            new() { Id = Guid.NewGuid(), Name = "مشروبات غازية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "عصائر طبيعية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مشروبات ساخنة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مياه ومشروبات طاقة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },

            // تصنيفات فرعية للإلكترونيات
            new() { Id = Guid.NewGuid(), Name = "هواتف ذكية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أجهزة كمبيوتر", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أجهزة منزلية كهربائية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "اكسسوارات إلكترونية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أجهزة صوتية ومرئية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },

            // تصنيفات فرعية للملابس
            new() { Id = Guid.NewGuid(), Name = "ملابس رجالية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "ملابس نسائية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "ملابس أطفال", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أحذية رجالية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أحذية نسائية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أحذية أطفال", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },

            // تصنيفات إضافية متنوعة
            new() { Id = Guid.NewGuid(), Name = "مواد بناء", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات كهربائية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "قطع غيار سيارات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أثاث وديكور", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "حدائق ونباتات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "كتب ومجلات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مجوهرات واكسسوارات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "حقائب ومحافظ", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "ساعات ونظارات", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "عطور ومستحضرات تجميل", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات مطبخ", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات حمام", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مستلزمات مكتبية متقدمة", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "أدوات فنية وحرفية", CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now }
        };

        _db.Categories.AddRange(categories);
    }

    /// <summary>
    /// إضافة المخازن الأساسية - 50 مخزن
    /// </summary>
    private async Task SeedStores()
    {
        var stores = new List<Store>();

        // المخازن الرئيسية
        var mainStores = new List<Store>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "المخزن الرئيسي",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = "0912345678",
                Address = "طرابلس - شارع الجمهورية",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "مخزن الفرع الأول",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = "0913456789",
                Address = "بنغازي - شارع عمر المختار",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "مخزن الفرع الثاني",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = "0914567890",
                Address = "مصراتة - شارع طرابلس",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "مخزن المواد الغذائية",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = "0915678901",
                Address = "الزاوية - المنطقة الصناعية",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "مخزن الطوارئ",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = "0916789012",
                Address = "سبها - المنطقة التجارية",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            }
        };

        stores.AddRange(mainStores);

        // إضافة 45 مخزن إضافي للوصول إلى 50 مخزن
        var cities = new[] { "طرابلس", "بنغازي", "مصراتة", "الزاوية", "سبها", "غريان", "زليتن", "أجدابيا", "درنة", "توكرة", "البيضاء", "شحات", "طبرق", "الكفرة", "مرزق", "غات", "أوباري", "هون", "سرت", "راس لانوف", "بني وليد", "ترهونة", "الخمس", "زوارة", "صبراتة", "العجيلات", "الجميل", "القره بوللي", "تاجوراء", "جنزور", "العزيزية", "القرابوللي", "مسلاتة", "بني وليد", "ودان", "سوكنة", "الجفرة", "زلة", "الأبيار", "المرج", "القبة", "المخيلي", "الرجبان", "الواحات", "الجغبوب" };
        var storeTypes = new[] { "مخزن", "مستودع", "فرع", "مركز توزيع", "نقطة تجميع" };
        var areas = new[] { "المنطقة الصناعية", "المنطقة التجارية", "وسط المدينة", "المنطقة الشرقية", "المنطقة الغربية", "المنطقة الشمالية", "المنطقة الجنوبية" };

        for (int i = 6; i <= 50; i++)
        {
            var city = cities[(i - 6) % cities.Length];
            var storeType = storeTypes[(i - 6) % storeTypes.Length];
            var area = areas[(i - 6) % areas.Length];
            var phoneBase = ********* + i;

            stores.Add(new Store
            {
                Id = Guid.NewGuid(),
                Name = $"{storeType} {city} {i - 5}",
                CompanyName = "شركة التقنية المتقدمة",
                PhoneNumber = $"0{phoneBase}",
                Address = $"{city} - {area}",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            });
        }

        _db.Stores.AddRange(stores);
    }



    /// <summary>
    /// إضافة العملاء والموردين بشكل تدريجي لتجنب مشاكل المهلة الزمنية
    /// </summary>
    private async Task SeedClientsAndSuppliersInBatches()
    {
        try
        {
            Console.WriteLine("بدء إضافة الموردين بشكل تدريجي...");

            // إضافة الموردين على دفعات
            await SeedSuppliersInBatches();

            Console.WriteLine("بدء إضافة العملاء بشكل تدريجي...");

            // إضافة العملاء على دفعات
            await SeedCustomersInBatches();

            Console.WriteLine("تم الانتهاء من إضافة جميع العملاء والموردين.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة العملاء والموردين: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// إضافة الموردين على دفعات
    /// </summary>
    private async Task SeedSuppliersInBatches()
    {
        var batchSize = 50; // إضافة 50 مورد في كل دفعة
        var totalSuppliers = 500;

        // الموردين الأساسيين
        var basicSuppliers = new List<Client>
        {
            new() { Id = Guid.NewGuid(), Name = "شركة الأغذية الليبية", Phone = 912345678, Address = "طرابلس - المنطقة الصناعية", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مؤسسة المشروبات الوطنية", Phone = 913456789, Address = "بنغازي - شارع الجلاء", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "شركة الألبان الطازجة", Phone = 914567890, Address = "مصراتة - المنطقة الزراعية", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مجمع اللحوم الليبي", Phone = 915678901, Address = "الزاوية - المجمع التجاري", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "شركة الخضروات والفواكه", Phone = 916789012, Address = "سبها - السوق المركزي", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مؤسسة مواد التنظيف", Phone = 917890123, Address = "طرابلس - شارع الفتح", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "شركة الأدوات المنزلية", Phone = 918901234, Address = "بنغازي - شارع الكورنيش", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مجموعة الإلكترونيات الحديثة", Phone = 919012345, Address = "مصراتة - المركز التجاري", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "شركة المستلزمات الطبية", Phone = 920123456, Address = "الزاوية - المنطقة الطبية", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مؤسسة القرطاسية والمكتبية", Phone = 921234567, Address = "سبها - شارع الجامعة", Balance = 0, IsSupplier = true, IsCustomer = false, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now }
        };

        // حفظ الموردين الأساسيين
        _db.Clients.AddRange(basicSuppliers);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ الموردين الأساسيين (10 موردين).");

        // إضافة باقي الموردين على دفعات
        var cities = new[] { "طرابلس", "بنغازي", "مصراتة", "الزاوية", "سبها", "غريان", "زليتن", "أجدابيا", "درنة", "توكرة", "البيضاء", "شحات", "طبرق", "الكفرة", "مرزق", "غات", "أوباري", "هون", "سرت", "راس لانوف" };
        var companyTypes = new[] { "شركة", "مؤسسة", "مجموعة", "مصنع", "معمل", "مجمع", "دار", "بيت", "متجر", "مركز" };
        var businessTypes = new[] { "التجارة العامة", "الاستيراد والتصدير", "المواد الغذائية", "الإلكترونيات", "الأدوات المنزلية", "المستلزمات الطبية", "مواد البناء", "قطع الغيار", "الملابس والأحذية", "الأثاث والديكور", "المواد الكيميائية", "الأجهزة الكهربائية", "اللوازم المكتبية", "المعدات الصناعية", "المنتجات الزراعية" };

        var addedSuppliers = 10; // الموردين الأساسيين

        while (addedSuppliers < totalSuppliers)
        {
            var currentBatchSize = Math.Min(batchSize, totalSuppliers - addedSuppliers);
            var batchSuppliers = new List<Client>();

            for (int i = 0; i < currentBatchSize; i++)
            {
                var supplierIndex = addedSuppliers + i + 1;
                var city = cities[(supplierIndex - 11) % cities.Length];
                var companyType = companyTypes[(supplierIndex - 11) % companyTypes.Length];
                var businessType = businessTypes[(supplierIndex - 11) % businessTypes.Length];
                var phoneBase = ********* + supplierIndex;

                batchSuppliers.Add(new Client
                {
                    Id = Guid.NewGuid(),
                    Name = $"{companyType} {businessType} {city} رقم {supplierIndex}",
                    Phone = phoneBase,
                    Address = $"{city} - المنطقة التجارية",
                    Balance = 0,
                    IsSupplier = true,
                    IsCustomer = false,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });
            }

            _db.Clients.AddRange(batchSuppliers);
            await _db.SaveChangesAsync();

            addedSuppliers += currentBatchSize;
            Console.WriteLine($"تم حفظ {addedSuppliers} من {totalSuppliers} مورد...");
        }

        Console.WriteLine($"تم الانتهاء من إضافة جميع الموردين ({totalSuppliers} مورد).");
    }

    /// <summary>
    /// إضافة العملاء على دفعات
    /// </summary>
    private async Task SeedCustomersInBatches()
    {
        var batchSize = 50; // إضافة 50 عميل في كل دفعة
        var totalCustomers = 300;

        // العملاء الأساسيين
        var basicCustomers = new List<Client>
        {
            new() { Id = Guid.NewGuid(), Name = "أحمد محمد الطرابلسي", Phone = 911111111, Address = "طرابلس - حي الأندلس", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "فاطمة علي البنغازية", Phone = 912222222, Address = "بنغازي - حي الصابري", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "محمد سالم المصراتي", Phone = 913333333, Address = "مصراتة - حي الشهداء", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "عائشة أحمد الزاوية", Phone = 914444444, Address = "الزاوية - حي النصر", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "عبدالله محمد السبهاوي", Phone = 915555555, Address = "سبها - حي الجامعة", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "خديجة سالم الطرابلسية", Phone = 916666666, Address = "طرابلس - حي الدهماني", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "يوسف علي البنغازي", Phone = 917777777, Address = "بنغازي - حي الليثي", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "مريم محمد المصراتية", Phone = 918888888, Address = "مصراتة - حي الزروق", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "إبراهيم أحمد الزاوي", Phone = 919999999, Address = "الزاوية - حي الوحدة", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now },
            new() { Id = Guid.NewGuid(), Name = "زينب سالم السبهاوية", Phone = 920000000, Address = "سبها - حي السلام", Balance = 0, IsSupplier = false, IsCustomer = true, CreatedAt = DateTime.Now, UpdatedAt = DateTime.Now }
        };

        // حفظ العملاء الأساسيين
        _db.Clients.AddRange(basicCustomers);
        await _db.SaveChangesAsync();
        Console.WriteLine("تم حفظ العملاء الأساسيين (10 عملاء).");

        // إضافة باقي العملاء على دفعات
        var cities = new[] { "طرابلس", "بنغازي", "مصراتة", "الزاوية", "سبها", "غريان", "زليتن", "أجدابيا", "درنة", "توكرة", "البيضاء", "شحات", "طبرق", "الكفرة", "مرزق", "غات", "أوباري", "هون", "سرت", "راس لانوف" };
        var maleNames = new[] { "أحمد", "محمد", "علي", "عبدالله", "إبراهيم", "يوسف", "عمر", "سالم", "خالد", "حسن", "عبدالرحمن", "مصطفى", "طارق", "ياسر", "وليد", "سعد", "فيصل", "ماجد", "نبيل", "رامي" };
        var femaleNames = new[] { "فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نور", "هدى", "أمل", "رقية", "حليمة", "سعاد", "نادية", "ليلى", "سلمى", "دعاء", "إيمان", "هالة", "منى", "رانيا" };
        var lastNames = new[] { "الطرابلسي", "البنغازي", "المصراتي", "الزاوي", "السبهاوي", "الغرياني", "الزليتني", "الأجدابي", "الدرناوي", "التوكري", "البيضاوي", "الشحاتي", "الطبرقي", "الكفراوي", "المرزقي", "الغاتي", "الأوباري", "الهوني", "السرتي", "الرأس لانوفي" };
        var neighborhoods = new[] { "حي الأندلس", "حي الصابري", "حي الشهداء", "حي النصر", "حي الجامعة", "حي الدهماني", "حي الليثي", "حي الزروق", "حي الوحدة", "حي السلام", "حي الفتح", "حي الكورنيش", "حي المركز", "حي الصناعي", "حي التجاري" };

        var addedCustomers = 10; // العملاء الأساسيين

        while (addedCustomers < totalCustomers)
        {
            var currentBatchSize = Math.Min(batchSize, totalCustomers - addedCustomers);
            var batchCustomers = new List<Client>();

            for (int i = 0; i < currentBatchSize; i++)
            {
                var customerIndex = addedCustomers + i + 1;
                var isMale = customerIndex % 2 == 1;
                var firstName = isMale ? maleNames[(customerIndex - 11) % maleNames.Length] : femaleNames[(customerIndex - 11) % femaleNames.Length];
                var fatherName = maleNames[(customerIndex - 11 + 5) % maleNames.Length];
                var lastName = lastNames[(customerIndex - 11) % lastNames.Length];
                var city = cities[(customerIndex - 11) % cities.Length];
                var neighborhood = neighborhoods[(customerIndex - 11) % neighborhoods.Length];
                var phoneBase = 930000000 + customerIndex;

                batchCustomers.Add(new Client
                {
                    Id = Guid.NewGuid(),
                    Name = $"{firstName} {fatherName} {lastName} - {customerIndex}",
                    Phone = phoneBase,
                    Address = $"{city} - {neighborhood}",
                    Balance = 0,
                    IsSupplier = false,
                    IsCustomer = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });
            }

            _db.Clients.AddRange(batchCustomers);
            await _db.SaveChangesAsync();

            addedCustomers += currentBatchSize;
            Console.WriteLine($"تم حفظ {addedCustomers} من {totalCustomers} عميل...");
        }

        Console.WriteLine($"تم الانتهاء من إضافة جميع العملاء ({totalCustomers} عميل).");
    }

    /// <summary>
    /// إضافة المستخدمين الإضافيين (بعد التأكد من وجود الأدوار)
    /// </summary>
    private async Task SeedAdditionalUsers()
    {
        try
        {
            Console.WriteLine("فحص المستخدمين الموجودين...");

            // التحقق من عدم وجود مستخدمين إضافيين مسبقاً
            var existingUsersCount = await _db.Users.CountAsync();
            Console.WriteLine($"عدد المستخدمين الموجودين: {existingUsersCount}");

            if (existingUsersCount > 1) // أكثر من المستخدم الافتراضي (Admin)
            {
                Console.WriteLine("المستخدمون الإضافيون موجودون بالفعل.");
                return; // المستخدمون موجودون بالفعل
            }

            // التأكد من وجود الخزائن قبل إنشاء المستخدمين
            Console.WriteLine("جلب الخزائن...");
            var treasuries = await _db.Treasuries.ToListAsync();
            Console.WriteLine($"تم العثور على {treasuries.Count} خزينة.");

            // التأكد من وجود الأدوار المطلوبة بطريقة محسنة
            var requiredRoles = new[] { "موظف المبيعات", "مسؤول المشتريات", "محاسب", "مدير المخزون" };
            Console.WriteLine("فحص الأدوار المطلوبة...");

            foreach (var roleName in requiredRoles)
            {
                var roleExists = await CheckRoleExistsWithTimeout(roleName);
                if (!roleExists)
                {
                    Console.WriteLine($"تحذير: الدور '{roleName}' غير موجود. سيتم تخطي إنشاء المستخدمين الإضافيين.");
                    return;
                }
            }

            Console.WriteLine("جميع الأدوار المطلوبة موجودة. بدء إنشاء المستخدمين الإضافيين...");

            // قائمة شاملة من المستخدمين - 49 مستخدم إضافي (مع المدير = 50)
            var users = new List<(string Name, string UserName, string RoleName)>();

            // المستخدمون الأساسيون
            var basicUsers = new List<(string Name, string UserName, string RoleName)>
            {
                ("سارة أحمد الطرابلسي", "Sara_Ahmed", "موظف المبيعات"),
                ("محمد علي البنغازي", "Mohamed_Ali", "موظف المبيعات"),
                ("عبدالله إبراهيم الطرابلسي", "Abdullah_Ibrahim", "مسؤول المشتريات"),
                ("مريم محمد البنغازية", "Mariam_Mohamed", "مسؤول المشتريات"),
                ("زينب محمد الطرابلسية", "Zeinab_Mohamed", "محاسب"),
                ("عمر أحمد البنغازي", "Omar_Ahmed", "محاسب"),
                ("سعد محمد الطرابلسي", "Saad_Mohamed", "مدير المخزون"),
                ("ليلى أحمد البنغازية", "Laila_Ahmed", "مدير المخزون")
            };

            users.AddRange(basicUsers);

            // إضافة 41 مستخدم إضافي للوصول إلى 49 (مع المدير = 50)
            var maleNames = new[] { "أحمد", "محمد", "علي", "عبدالله", "إبراهيم", "يوسف", "عمر", "سالم", "خالد", "حسن", "عبدالرحمن", "مصطفى", "طارق", "ياسر", "وليد", "سعد", "فيصل", "ماجد", "نبيل", "رامي", "هشام" };
            var femaleNames = new[] { "فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نور", "هدى", "أمل", "رقية", "حليمة", "سعاد", "نادية", "ليلى", "سلمى", "دعاء", "إيمان", "هالة", "منى", "رانيا", "نجلاء" };
            var lastNames = new[] { "الطرابلسي", "البنغازي", "المصراتي", "الزاوي", "السبهاوي", "الغرياني", "الزليتني", "الأجدابي", "الدرناوي", "التوكري" };
            var roles = new[] { "موظف المبيعات", "مسؤول المشتريات", "محاسب", "مدير المخزون" };

            for (int i = 9; i <= 49; i++)
            {
                var isMale = i % 2 == 1;
                var firstName = isMale ? maleNames[(i - 9) % maleNames.Length] : femaleNames[(i - 9) % femaleNames.Length];
                var fatherName = maleNames[(i - 9 + 3) % maleNames.Length];
                var lastName = lastNames[(i - 9) % lastNames.Length];
                var role = roles[(i - 9) % roles.Length];
                var userName = $"{firstName}_{fatherName}_{i}";

                users.Add((
                    Name: $"{firstName} {fatherName} {lastName} - {i}",
                    UserName: userName,
                    RoleName: role
                ));
            }

            foreach (var (name, userName, roleName) in users)
            {
                try
                {
                    Console.WriteLine($"إنشاء المستخدم: {userName}");

                    // التحقق من عدم وجود المستخدم مسبقاً
                    var existingUser = await user.FindByNameAsync(userName);
                    if (existingUser != null)
                    {
                        Console.WriteLine($"المستخدم {userName} موجود بالفعل.");
                        continue; // المستخدم موجود بالفعل
                    }

                    var newUser = new User
                    {
                        Id = Guid.NewGuid(),
                        Name = name,
                        UserName = userName,
                        Email = userName.ToLower() + "@postech.com",
                        EmailConfirmed = true,
                        UserTreasuries = treasuries.Select(t => new UserTreasury
                        {
                            TreasuryId = t.Id,
                            Balance = 0
                        }).ToList()
                    };

                    var result = await user.CreateAsync(newUser, "Admin123*");

                    if (result.Succeeded)
                    {
                        Console.WriteLine($"تم إنشاء المستخدم {userName} بنجاح.");

                        // إضافة الدور للمستخدم
                        try
                        {
                            await user.AddToRoleAsync(newUser, roleName);
                            Console.WriteLine($"تم إضافة الدور {roleName} للمستخدم {userName}.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في إضافة الدور '{roleName}' للمستخدم '{userName}': {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"فشل في إنشاء المستخدم '{userName}': {string.Join(", ", result.Errors.Select(e => e.Description))}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ عام في إنشاء المستخدم {userName}: {ex.Message}");
                }
            }

            Console.WriteLine("تم الانتهاء من إنشاء المستخدمين الإضافيين.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة المستخدمين الإضافيين: {ex.Message}");
        }
    }

    /// <summary>
    /// إضافة الأصناف مع وحداتها
    /// </summary>
    private async Task SeedItemsWithUnits()
    {
        try
        {
            Console.WriteLine("بدء إضافة الأصناف...");

            // الحصول على البيانات المرجعية
            var categories = await _db.Categories.ToListAsync();
            var units = await _db.Units.ToListAsync();

            Console.WriteLine($"تم العثور على {categories.Count} تصنيف و {units.Count} وحدة قياس");

            if (!categories.Any() || !units.Any())
            {
                Console.WriteLine("خطأ: لا توجد تصنيفات أو وحدات قياس!");
                return;
            }

            // تجميع الوحدات حسب النوع لسهولة الوصول
            var pieceUnit = units.FirstOrDefault(u => u.Name == "قطعة");
            var kiloUnit = units.FirstOrDefault(u => u.Name == "كيلو");
            var gramUnit = units.FirstOrDefault(u => u.Name == "جرام");
            var literUnit = units.FirstOrDefault(u => u.Name == "لتر");
            var mlUnit = units.FirstOrDefault(u => u.Name == "مليلتر");
            var dozenUnit = units.FirstOrDefault(u => u.Name == "دستة");
            var cartonUnit = units.FirstOrDefault(u => u.Name == "كرتون");
            var boxUnit = units.FirstOrDefault(u => u.Name == "علبة");
            var bagUnit = units.FirstOrDefault(u => u.Name == "كيس");
            var bottleUnit = units.FirstOrDefault(u => u.Name == "زجاجة");

            // تجميع التصنيفات حسب النوع
            var foodCategory = categories.FirstOrDefault(c => c.Name == "مواد غذائية");
            var drinksCategory = categories.FirstOrDefault(c => c.Name == "مشروبات");
            var dairyCategory = categories.FirstOrDefault(c => c.Name == "منتجات الألبان");
            var meatCategory = categories.FirstOrDefault(c => c.Name == "لحوم ودواجن");
            var vegetablesCategory = categories.FirstOrDefault(c => c.Name == "خضروات وفواكه");
            var cleaningCategory = categories.FirstOrDefault(c => c.Name == "مواد تنظيف");
            var householdCategory = categories.FirstOrDefault(c => c.Name == "أدوات منزلية");
            var personalCategory = categories.FirstOrDefault(c => c.Name == "مستلزمات شخصية");
            var medicineCategory = categories.FirstOrDefault(c => c.Name == "أدوية ومستلزمات طبية");
            var stationeryCategory = categories.FirstOrDefault(c => c.Name == "قرطاسية ومكتبية");

            Console.WriteLine("بدء إضافة أصناف المواد الغذائية...");
            // إنشاء الأصناف
            await SeedFoodItems(foodCategory, pieceUnit, kiloUnit, gramUnit, bagUnit, boxUnit, cartonUnit, dozenUnit);

            Console.WriteLine("بدء إضافة أصناف المشروبات...");
            await SeedDrinkItems(drinksCategory, bottleUnit, literUnit, mlUnit, cartonUnit, dozenUnit, pieceUnit, boxUnit);

            Console.WriteLine("بدء إضافة أصناف منتجات الألبان...");
            await SeedDairyItems(dairyCategory, literUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, mlUnit);

            Console.WriteLine("بدء إضافة أصناف اللحوم...");
            await SeedMeatItems(meatCategory, kiloUnit, gramUnit, pieceUnit, bagUnit);

            Console.WriteLine("بدء إضافة أصناف الخضروات...");
            await SeedVegetableItems(vegetablesCategory, kiloUnit, gramUnit, pieceUnit, bagUnit);

            Console.WriteLine("بدء إضافة أصناف مواد التنظيف...");
            await SeedCleaningItems(cleaningCategory, literUnit, mlUnit, kiloUnit, gramUnit, bottleUnit, boxUnit, pieceUnit, dozenUnit);

            Console.WriteLine("بدء إضافة أصناف الأدوات المنزلية...");
            await SeedHouseholdItems(householdCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);

            Console.WriteLine("بدء إضافة أصناف المستلزمات الشخصية...");
            await SeedPersonalItems(personalCategory, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);

            Console.WriteLine("بدء إضافة أصناف الأدوية...");
            await SeedMedicineItems(medicineCategory, pieceUnit, bottleUnit, boxUnit, gramUnit, mlUnit);

            Console.WriteLine("بدء إضافة أصناف القرطاسية...");
            await SeedStationeryItems(stationeryCategory, pieceUnit, boxUnit, cartonUnit, dozenUnit);

            Console.WriteLine("تم الانتهاء من إضافة جميع الأصناف!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة الأصناف: {ex.Message}");
            Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// إضافة أصناف المواد الغذائية
    /// </summary>
    private async Task SeedFoodItems(Category category, Unit pieceUnit, Unit kiloUnit, Unit gramUnit, Unit bagUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var foodItems = new List<(string Name, decimal CostPrice, bool HasExp, List<(Unit Unit, decimal Quantity, bool IsBasic, bool IsBigger, decimal SalePrice)> Units)>
        {
            // الحبوب والنشويات
            ("أرز بسمتي", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 10.00m) }),
            ("أرز مصري", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.00m) }),
            ("أرز أبيض عادي", 5.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("برغل ناعم", 4.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.50m) }),
            ("برغل خشن", 4.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 6.00m) }),
            ("شعير", 3.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.00m) }),
            ("شوفان", 7.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 9.00m) }),
            ("كينوا", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 30.00m) }),
            ("عدس أحمر", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.00m) }),
            ("عدس أصفر", 5.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.50m) }),
            ("فول مدمس", 4.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.50m) }),
            ("حمص حب", 7.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 9.50m) }),
            ("فاصوليا بيضاء", 8.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 10.50m) }),
            ("فاصوليا حمراء", 9.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 12.00m) }),
            ("لوبيا", 6.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),

            // السكريات والمحليات
            ("سكر أبيض", 3.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.00m) }),
            ("سكر بني", 4.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 6.00m) }),
            ("عسل طبيعي", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 45.00m) }),
            ("دبس التمر", 18.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("مربى فراولة", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("مربى مشمش", 13.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 17.00m) }),
            ("مربى تين", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),

            // الدقيق والمخبوزات
            ("دقيق أبيض", 2.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.50m) }),
            ("دقيق أسمر", 3.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.50m) }),
            ("دقيق ذرة", 4.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.50m) }),
            ("سميد ناعم", 3.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.00m) }),
            ("سميد خشن", 4.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.20m) }),
            ("خميرة فورية", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 11.00m) }),
            ("بيكنج باودر", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 8.50m) }),

            // الزيوت والدهون
            ("زيت طبخ", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("زيت زيتون", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("زيت عباد الشمس", 14.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 18.00m) }),
            ("زيت الذرة", 16.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 20.00m) }),
            ("سمن بلدي", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("زبدة طبخ", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),

            // المشروبات الساخنة
            ("شاي أحمر", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 22.00m) }),
            ("شاي أخضر", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 30.00m) }),
            ("قهوة عربية", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 45.00m) }),
            ("قهوة تركية", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("نسكافيه", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("كاكاو", 32.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 40.00m) }),
            ("شاي بالنعناع", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 28.00m) }),

            // التوابل والبهارات
            ("ملح طعام", 1.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 2.00m) }),
            ("فلفل أسود", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 30.00m) }),
            ("كمون", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 22.00m) }),
            ("كزبرة", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 18.00m) }),
            ("هيل", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 100.00m) }),
            ("قرفة", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 42.00m) }),
            ("زنجبيل", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 35.00m) }),
            ("كركم", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 28.00m) }),
            ("بابريكا", 20.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 25.00m) }),
            ("بهارات مشكلة", 30.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 100, true, false, 38.00m) }),

            // المعلبات والمحفوظات
            ("طماطم معلبة", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 6.00m) }),
            ("ذرة معلبة", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 7.00m) }),
            ("فاصوليا معلبة", 6.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.00m) }),
            ("تونة معلبة", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("سردين معلب", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),
            ("زيتون أخضر", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("زيتون أسود", 14.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 17.00m) }),
            ("مخلل مشكل", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),

            // المكسرات والفواكه المجففة
            ("لوز", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("جوز", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 68.00m) }),
            ("فستق", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 105.00m) }),
            ("كاجو", 75.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 92.00m) }),
            ("بندق", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 80.00m) }),
            ("تمر", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("زبيب", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("تين مجفف", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("مشمش مجفف", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) })
        };

        await CreateItemsWithUnits(foodItems, category);
    }

    /// <summary>
    /// إضافة أصناف المشروبات
    /// </summary>
    private async Task SeedDrinkItems(Category category, Unit bottleUnit, Unit literUnit, Unit mlUnit, Unit cartonUnit, Unit dozenUnit, Unit pieceUnit, Unit boxUnit)
    {
        if (category == null) return;

        var drinkItems = new List<(string Name, decimal CostPrice, bool HasExp, List<(Unit Unit, decimal Quantity, bool IsBasic, bool IsBigger, decimal SalePrice)> Units)>
        {
            // المياه
            ("مياه معدنية صغيرة", 0.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 1.00m) }),
            ("مياه معدنية كبيرة", 1.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 2.00m) }),
            ("مياه غازية", 1.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 1.50m) }),
            ("مياه منكهة", 2.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 2.50m) }),

            // العصائر الطبيعية
            ("عصير برتقال طبيعي", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 4.50m) }),
            ("عصير تفاح طبيعي", 4.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.00m) }),
            ("عصير عنب طبيعي", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.50m) }),
            ("عصير مانجو طبيعي", 5.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 6.50m) }),
            ("عصير جوافة طبيعي", 4.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.20m) }),
            ("عصير فراولة طبيعي", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 7.00m) }),
            ("عصير أناناس طبيعي", 6.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 7.50m) }),

            // العصائر المركزة
            ("عصير برتقال مركز", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 11.00m) }),
            ("عصير ليمون مركز", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 9.50m) }),
            ("عصير رمان مركز", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 15.00m) }),
            ("عصير توت مركز", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 18.50m) }),

            // المشروبات الغازية
            ("كوكا كولا", 2.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.50m) }),
            ("بيبسي", 2.70m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.40m) }),
            ("سفن أب", 2.60m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.30m) }),
            ("فانتا برتقال", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.20m) }),
            ("فانتا عنب", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.20m) }),
            ("سبرايت", 2.60m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.30m) }),
            ("ميرندا", 2.40m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.10m) }),

            // مشروبات الطاقة
            ("ريد بول", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 11.00m) }),
            ("مونستر", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 9.50m) }),
            ("باور هورس", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 8.50m) }),

            // المشروبات الرياضية
            ("جاتوريد", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 7.00m) }),
            ("باوريد", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 6.00m) }),

            // الشاي المثلج والقهوة الباردة
            ("شاي مثلج بالليمون", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 4.50m) }),
            ("شاي مثلج بالخوخ", 3.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 4.80m) }),
            ("قهوة باردة", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 8.50m) }),
            ("كابتشينو بارد", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 9.50m) }),

            // مشروبات صحية
            ("ماء جوز الهند", 8.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 10.50m) }),
            ("مشروب الصبار", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 8.50m) }),
            ("عصير أخضر", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 15.00m) }),
            ("مشروب البروتين", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 19.00m) }),

            // المشروبات التقليدية
            ("تمر هندي", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 6.00m) }),
            ("كركديه", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 4.50m) }),
            ("عرق سوس", 4.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.20m) }),
            ("شراب الورد", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 7.00m) }),

            // مشروبات للأطفال
            ("عصير أطفال تفاح", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.20m) }),
            ("عصير أطفال برتقال", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 3.20m) }),
            ("حليب بالشوكولاتة", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.80m) }),
            ("حليب بالفراولة", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 5.80m) }),

            // مشروبات ساخنة جاهزة
            ("سحلب جاهز", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("شوكولاتة ساخنة جاهزة", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("قهوة فورية 3 في 1", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 23.00m) }),
            ("شاي أخضر بالياسمين", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 32.00m) })
        };

        await CreateItemsWithUnits(drinkItems, category);
    }

    /// <summary>
    /// إضافة أصناف منتجات الألبان
    /// </summary>
    private async Task SeedDairyItems(Category category, Unit literUnit, Unit kiloUnit, Unit gramUnit, Unit bottleUnit, Unit boxUnit, Unit pieceUnit, Unit mlUnit)
    {
        if (category == null) return;

        var dairyItems = new List<(string Name, decimal CostPrice, bool HasExp, List<(Unit Unit, decimal Quantity, bool IsBasic, bool IsBigger, decimal SalePrice)> Units)>
        {
            // الحليب ومشتقاته
            ("حليب طازج كامل الدسم", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 5.50m) }),
            ("حليب قليل الدسم", 4.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 5.20m) }),
            ("حليب خالي الدسم", 4.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 6.00m) }),
            ("حليب مبستر", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 7.00m) }),
            ("حليب طويل الأمد", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 8.00m) }),
            ("حليب بودرة", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 45.00m) }),
            ("حليب مكثف محلى", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("حليب مبخر", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),

            // الأجبان البيضاء
            ("جبنة بيضاء طرية", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 22.00m) }),
            ("جبنة بيضاء مالحة", 16.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 20.50m) }),
            ("جبنة عكاوي", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("جبنة حلوم", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("جبنة قريش", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("جبنة ريكوتا", 28.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),

            // الأجبان الصفراء
            ("جبنة شيدر", 32.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 40.00m) }),
            ("جبنة موزاريلا", 28.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("جبنة إيدام", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("جبنة جودة", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("جبنة إيمنتال", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("جبنة بارميزان", 85.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 105.00m) }),

            // الأجبان المدخنة والمتبلة
            ("جبنة مدخنة", 42.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 52.00m) }),
            ("جبنة بالأعشاب", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("جبنة بالفلفل الأسود", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),

            // الزبدة والكريمة
            ("زبدة طبيعية", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 500, true, false, 30.00m) }),
            ("زبدة مملحة", 23.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 500, true, false, 28.00m) }),
            ("زبدة غير مملحة", 27.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (gramUnit, 500, true, false, 33.00m) }),
            ("كريمة طبخ", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (mlUnit, 200, true, false, 15.00m) }),
            ("كريمة خفق", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (mlUnit, 200, true, false, 18.50m) }),
            ("كريمة حامضة", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (mlUnit, 200, true, false, 22.00m) }),

            // اللبن والزبادي
            ("لبن رائب", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 4.50m) }),
            ("زبادي طبيعي", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("زبادي بالفواكه", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("زبادي يوناني", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("لبنة", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("لبنة بالزعتر", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),

            // منتجات أخرى
            ("آيس كريم فانيليا", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 28.00m) }),
            ("آيس كريم شوكولاتة", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 32.00m) }),
            ("آيس كريم فراولة", 24.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 30.00m) }),
            ("مهلبية جاهزة", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("كسترد جاهز", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),
            ("بودنغ شوكولاتة", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 9.50m) }),

            // منتجات الأطفال
            ("حليب أطفال", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("زبادي أطفال", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),
            ("جبنة مثلثات", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 15.00m) }),

            // منتجات نباتية
            ("حليب لوز", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 23.00m) }),
            ("حليب جوز الهند", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 28.00m) }),
            ("حليب الشوفان", 20.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 25.00m) }),
            ("زبادي نباتي", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) })
        };

        await CreateItemsWithUnits(dairyItems, category);
    }

    /// <summary>
    /// ميثود مساعدة لإنشاء الأصناف مع وحداتها
    /// </summary>
    private async Task CreateItemsWithUnits(List<(string Name, decimal CostPrice, bool HasExp, List<(Unit Unit, decimal Quantity, bool IsBasic, bool IsBigger, decimal SalePrice)> Units)> itemsData, Category category)
    {
        try
        {
            if (category == null)
            {
                Console.WriteLine("خطأ: التصنيف فارغ!");
                return;
            }

            Console.WriteLine($"إضافة {itemsData.Count} صنف للتصنيف: {category.Name}");

            foreach (var (name, costPrice, hasExp, units) in itemsData)
            {
                Console.WriteLine($"إضافة الصنف: {name}");

                var item = new Item
                {
                    Id = Guid.NewGuid(),
                    Name = name,
                    CategoryId = category.Id,
                    CostPrice = costPrice,
                    IsHaveExp = hasExp,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _db.Items.Add(item);

                // إضافة وحدات الصنف
                foreach (var (unit, quantity, isBasic, isBigger, salePrice) in units)
                {
                    if (unit != null)
                    {
                        Console.WriteLine($"  إضافة وحدة: {unit.Name} للصنف: {name}");

                        var itemUnit = new ItemUnit
                        {
                            Id = Guid.NewGuid(),
                            ItemId = item.Id,
                            UnitId = unit.Id,
                            Quantity = quantity,
                            IsBasicUnit = isBasic,
                            IsBigger = isBigger,
                            SalePrice = salePrice,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };

                        _db.ItemUnits.Add(itemUnit);
                    }
                    else
                    {
                        Console.WriteLine($"  تحذير: وحدة فارغة للصنف: {name}");
                    }
                }
            }

            Console.WriteLine($"تم إضافة جميع أصناف التصنيف: {category.Name}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة أصناف التصنيف {category?.Name}: {ex.Message}");
        }
    }

    // إضافة باقي الميثودز للأصناف الأخرى (مبسطة لتوفير المساحة)
    private async Task SeedMeatItems(Category category, Unit kiloUnit, Unit gramUnit, Unit pieceUnit, Unit bagUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // لحوم البقر
            ("لحم بقري عادي", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("لحم بقري ممتاز", 55.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 68.00m) }),
            ("ستيك بقري", 65.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 80.00m) }),
            ("لحم بقري مفروم", 42.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 52.00m) }),
            ("كبدة بقري", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 42.00m) }),
            ("كلاوي بقري", 28.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("قلب بقري", 32.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 40.00m) }),

            // لحوم الغنم
            ("لحم غنم عادي", 50.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 60.00m) }),
            ("لحم غنم ممتاز", 62.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 75.00m) }),
            ("لحم خروف صغير", 75.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 92.00m) }),
            ("لحم غنم مفروم", 48.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 58.00m) }),
            ("كبدة غنم", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("كلاوي غنم", 32.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 40.00m) }),

            // لحوم الماعز
            ("لحم ماعز", 58.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 70.00m) }),
            ("لحم جدي", 85.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 105.00m) }),

            // الدواجن
            ("دجاج كامل", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 22.00m) }),
            ("دجاج مقطع", 20.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 25.00m) }),
            ("صدر دجاج", 28.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("فخذ دجاج", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("أجنحة دجاج", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("كبدة دجاج", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("قوانص دجاج", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),

            // الديك الرومي
            ("ديك رومي كامل", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("صدر ديك رومي", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("فخذ ديك رومي", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),

            // البط والإوز
            ("بط كامل", 32.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 40.00m) }),
            ("صدر بط", 48.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 60.00m) }),
            ("إوز كامل", 55.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 68.00m) }),

            // الأسماك البحرية
            ("سمك طازج عادي", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 30.00m) }),
            ("سمك بوري", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("سمك دنيس", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("سمك لوت", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("سمك هامور", 65.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 80.00m) }),
            ("سمك تونة طازج", 85.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 105.00m) }),
            ("سمك سلمون", 125.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 155.00m) }),

            // الأسماك النهرية
            ("سمك بلطي", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("سمك قرموط", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("سمك مبروك", 20.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 25.00m) }),

            // المأكولات البحرية
            ("جمبري", 95.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 118.00m) }),
            ("كابوريا", 75.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 92.00m) }),
            ("حبار", 65.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 80.00m) }),
            ("أخطبوط", 85.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 105.00m) }),
            ("محار", 120.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 148.00m) }),

            // اللحوم المصنعة
            ("سجق بقري", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("سجق دجاج", 28.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 35.00m) }),
            ("برجر بقري", 32.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 40.00m) }),
            ("برجر دجاج", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("نقانق", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("مرتديلا", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("سلامي", 55.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 68.00m) }),

            // اللحوم المجمدة
            ("لحم بقري مجمد", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("دجاج مجمد", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("سمك مجمد", 20.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 25.00m) }),
            ("جمبري مجمد", 75.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 92.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedVegetableItems(Category category, Unit kiloUnit, Unit gramUnit, Unit pieceUnit, Unit bagUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // الخضروات الورقية
            ("خس", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.20m) }),
            ("جرجير", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.50m) }),
            ("سبانخ", 4.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.00m) }),
            ("ملوخية", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("بقدونس", 2.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.50m) }),
            ("كزبرة خضراء", 3.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.00m) }),
            ("شبت", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.50m) }),
            ("نعناع", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("ريحان", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("كرنب", 2.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 2.80m) }),

            // الخضروات الجذرية
            ("جزر", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.50m) }),
            ("فجل", 2.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.50m) }),
            ("لفت", 3.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.00m) }),
            ("شمندر", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("بطاطس", 3.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.80m) }),
            ("بطاطا حلوة", 5.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),

            // البصليات
            ("بصل أبيض", 2.80m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.50m) }),
            ("بصل أحمر", 3.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.00m) }),
            ("ثوم", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("بصل أخضر", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("كراث", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),

            // الطماطم والخيار
            ("طماطم", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.50m) }),
            ("طماطم كرزية", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("خيار", 3.20m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.00m) }),
            ("خيار مخلل", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),

            // الفلفل والباذنجان
            ("فلفل أخضر", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("فلفل أحمر", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("فلفل أصفر", 9.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 12.00m) }),
            ("فلفل حار", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("باذنجان", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("باذنجان أبيض", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),

            // القرعيات
            ("كوسة", 3.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.80m) }),
            ("قرع", 2.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.20m) }),
            ("خيار شمام", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("بطيخ", 2.80m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 3.50m) }),
            ("شمام", 3.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 4.50m) }),

            // البقوليات الطازجة
            ("فاصوليا خضراء", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),
            ("بازلاء خضراء", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("لوبيا خضراء", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("فول أخضر", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),

            // الفواكه الحمضية
            ("برتقال", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),
            ("ليمون", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),
            ("يوسفي", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("جريب فروت", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 9.50m) }),
            ("لايم", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),

            // الفواكه الأساسية
            ("تفاح أحمر", 8.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 10.00m) }),
            ("تفاح أخضر", 9.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 12.00m) }),
            ("موز", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),
            ("عنب أحمر", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("عنب أخضر", 11.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 14.00m) }),
            ("كمثرى", 9.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 12.00m) }),

            // الفواكه الاستوائية
            ("مانجو", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("أناناس", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("كيوي", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("أفوكادو", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("جوز الهند", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("باباي", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),

            // الفواكه الحجرية
            ("خوخ", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("مشمش", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("برقوق", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("كرز", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("نكتارين", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),

            // التوت والفراولة
            ("فراولة", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("توت أزرق", 45.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("توت أحمر", 38.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 47.00m) }),
            ("توت أسود", 42.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 52.00m) }),

            // فواكه أخرى
            ("رمان", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("تين", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("جوافة", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("قشطة", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),

            // الخضروات المجمدة
            ("خضار مشكلة مجمدة", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("بازلاء مجمدة", 6.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 8.50m) }),
            ("فاصوليا مجمدة", 7.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 9.50m) }),
            ("ذرة مجمدة", 5.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 7.00m) }),
            ("سبانخ مجمدة", 4.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 5.80m) }),

            // الفواكه المجمدة
            ("فراولة مجمدة", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("مانجو مجمدة", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("توت مشكل مجمد", 25.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),

            // الأعشاب والتوابل الطازجة
            ("زعتر أخضر", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 11.00m) }),
            ("روزماري", 12.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 15.00m) }),
            ("أوريجانو طازج", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 19.00m) }),
            ("حصالبان", 18.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedCleaningItems(Category category, Unit literUnit, Unit mlUnit, Unit kiloUnit, Unit gramUnit, Unit bottleUnit, Unit boxUnit, Unit pieceUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // منظفات الأطباق والمطبخ
            ("منظف أطباق عادي", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 10.50m) }),
            ("منظف أطباق مركز", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 15.00m) }),
            ("منظف أطباق بالليمون", 9.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 12.00m) }),
            ("منظف دهون المطبخ", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 18.50m) }),
            ("منظف الفرن", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 28.00m) }),
            ("منظف الميكروويف", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 23.00m) }),

            // مساحيق الغسيل
            ("مسحوق غسيل عادي", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 18.00m) }),
            ("مسحوق غسيل أوتوماتيك", 18.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("مسحوق غسيل للألوان", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 28.00m) }),
            ("مسحوق غسيل للأبيض", 20.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 25.00m) }),
            ("مسحوق غسيل للأطفال", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),
            ("مسحوق غسيل بالعطر", 19.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 24.50m) }),

            // منظفات سائلة للغسيل
            ("منظف سائل للغسيل", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 35.00m) }),
            ("منظف سائل مركز", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 43.00m) }),
            ("منعم الأقمشة", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 23.00m) }),
            ("مزيل البقع", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 32.00m) }),

            // منظفات الأرضيات
            ("منظف أرضيات عادي", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 15.00m) }),
            ("منظف أرضيات بالصنوبر", 14.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 18.00m) }),
            ("منظف أرضيات بالليمون", 13.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 17.00m) }),
            ("منظف أرضيات مطهر", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 23.00m) }),
            ("ملمع الأرضيات", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 28.00m) }),

            // منظفات الحمام
            ("منظف الحمام", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 19.00m) }),
            ("منظف المرحاض", 12.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 16.00m) }),
            ("منظف البلاط", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 23.00m) }),
            ("مزيل الكلس", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 28.00m) }),
            ("منظف الدش", 16.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 21.00m) }),

            // منظفات الزجاج والنوافذ
            ("منظف الزجاج", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 11.00m) }),
            ("منظف النوافذ", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 15.00m) }),
            ("منظف المرايا", 10.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 13.50m) }),

            // المطهرات والمعقمات
            ("مطهر عام", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 23.00m) }),
            ("كحول طبي", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 19.00m) }),
            ("مطهر اليدين", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 15.00m) }),
            ("مناديل مطهرة", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 11.00m) }),
            ("جل مطهر", 18.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 23.50m) }),

            // معطرات الجو
            ("معطر جو عادي", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 22.00m) }),
            ("معطر جو بالورد", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 28.00m) }),
            ("معطر جو بالياسمين", 20.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 25.00m) }),
            ("معطر جو بالليمون", 19.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 24.00m) }),
            ("معطر جو أوتوماتيك", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("شمع معطر", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),

            // المناديل والورقيات
            ("مناديل ورقية عادية", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 8.00m) }),
            ("مناديل ورقية ناعمة", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 11.00m) }),
            ("مناديل جيب", 4.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 5.80m) }),
            ("ورق تواليت", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (dozenUnit, 1, true, false, 15.00m) }),
            ("ورق مطبخ", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (dozenUnit, 1, true, false, 11.00m) }),
            ("مناشف ورقية", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (dozenUnit, 1, true, false, 19.00m) }),

            // أدوات التنظيف
            ("إسفنج تنظيف", 3.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 4.50m) }),
            ("فرشاة تنظيف", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("قفازات مطاطية", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),
            ("ممسحة أرضيات", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("دلو تنظيف", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),

            // منتجات خاصة
            ("مزيل الصدأ", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 28.00m) }),
            ("ملمع الأثاث", 18.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 23.50m) }),
            ("منظف السجاد", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 35.00m) }),
            ("مزيل الدهان", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 43.00m) }),
            ("منظف المعادن", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 32.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedHouseholdItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            ("أكواب بلاستيك", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (dozenUnit, 1, true, false, 15.00m) }),
            ("صحون ورقية", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 10.50m) }),
            ("أكياس نايلون", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (cartonUnit, 1, true, false, 30.00m) }),
            ("ملاعق بلاستيك", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 18.00m) }),
            ("شموع", 5.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 7.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedPersonalItems(Category category, Unit pieceUnit, Unit bottleUnit, Unit boxUnit, Unit gramUnit, Unit mlUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            ("شامبو", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 30.00m) }),
            ("صابون استحمام", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 10.50m) }),
            ("معجون أسنان", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("فرشاة أسنان", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.00m) }),
            ("كريم مرطب", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 22.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedMedicineItems(Category category, Unit pieceUnit, Unit bottleUnit, Unit boxUnit, Unit gramUnit, Unit mlUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            ("باراسيتامول", 8.50m, true, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 10.50m) }),
            ("شراب كحة", 15.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 18.00m) }),
            ("مرهم مضاد حيوي", 22.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 26.00m) }),
            ("فيتامين سي", 35.00m, true, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 42.00m) }),
            ("لاصق طبي", 12.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 15.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    private async Task SeedStationeryItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            ("أقلام حبر", 2.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 3.00m), (dozenUnit, 12, false, true, 32.00m) }),
            ("دفاتر", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 10.50m) }),
            ("ورق طباعة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 55.00m) }),
            ("مقص", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 18.00m) }),
            ("غراء", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف الملابس والأحذية
    /// </summary>
    private async Task SeedClothingItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // ملابس رجالية
            ("قميص رجالي", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("بنطلون رجالي", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("جاكيت رجالي", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("بدلة رجالية", 285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 350.00m) }),
            ("تي شيرت رجالي", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("شورت رجالي", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),

            // ملابس نسائية
            ("فستان نسائي", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("بلوزة نسائية", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 68.00m) }),
            ("تنورة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("بنطلون نسائي", 75.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 92.00m) }),
            ("جاكيت نسائي", 135.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 165.00m) }),
            ("تي شيرت نسائي", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 35.00m) }),

            // ملابس أطفال
            ("قميص أطفال", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 28.00m) }),
            ("بنطلون أطفال", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("فستان أطفال", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("تي شيرت أطفال", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("شورت أطفال", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),

            // الملابس الداخلية
            ("ملابس داخلية رجالية", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),
            ("ملابس داخلية نسائية", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("جوارب رجالية", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("جوارب نسائية", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("جوارب أطفال", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),

            // الأحذية الرجالية
            ("حذاء رجالي كلاسيكي", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("حذاء رجالي رياضي", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("صندل رجالي", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("شبشب رجالي", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),

            // الأحذية النسائية
            ("حذاء نسائي كلاسيكي", 95.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 118.00m) }),
            ("حذاء نسائي رياضي", 75.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 92.00m) }),
            ("حذاء كعب عالي", 115.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 142.00m) }),
            ("صندل نسائي", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 68.00m) }),
            ("شبشب نسائي", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 35.00m) }),

            // أحذية الأطفال
            ("حذاء أطفال", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("حذاء أطفال رياضي", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 68.00m) }),
            ("صندل أطفال", 32.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 40.00m) }),

            // الإكسسوارات
            ("حزام رجالي", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("حزام نسائي", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 35.00m) }),
            ("ربطة عنق", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 28.00m) }),
            ("قبعة", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("شال", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("قفازات", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف الإلكترونيات
    /// </summary>
    private async Task SeedElectronicsItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // الهواتف والاتصالات
            ("هاتف ذكي", 850.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 1050.00m) }),
            ("هاتف عادي", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("شاحن هاتف", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("كابل USB", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("سماعات أذن", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("سماعات بلوتوث", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),

            // أجهزة الكمبيوتر
            ("لابتوب", 1850.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 2300.00m) }),
            ("كمبيوتر مكتبي", 1250.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 1550.00m) }),
            ("شاشة كمبيوتر", 385.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 480.00m) }),
            ("لوحة مفاتيح", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("ماوس", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("طابعة", 285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 350.00m) }),

            // أجهزة التلفزيون والصوت
            ("تلفزيون LED", 1450.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 1800.00m) }),
            ("راديو", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("مسجل", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("سبيكر", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),

            // الكاميرات والتصوير
            ("كاميرا رقمية", 685.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 850.00m) }),
            ("كاميرا فيديو", 1285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 1600.00m) }),
            ("ترايبود", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),

            // الأجهزة المنزلية الصغيرة
            ("مكواة", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("مجفف شعر", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("خلاط", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("محضرة طعام", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("غلاية كهربائية", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("محمصة خبز", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),

            // البطاريات والطاقة
            ("بطارية AA", 3.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 4.50m) }),
            ("بطارية AAA", 3.20m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 4.00m) }),
            ("بطارية 9V", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("شاحن بطاريات", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("باور بانك", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),

            // الإكسسوارات الإلكترونية
            ("فلاش ميموري", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("قرص صلب خارجي", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("كارت ذاكرة", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("محول كهرباء", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 28.00m) }),
            ("مقبس متعدد", 18.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.50m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف الأدوات الرياضية
    /// </summary>
    private async Task SeedSportsItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // كرة القدم
            ("كرة قدم", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("حذاء كرة قدم", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("قميص كرة قدم", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("شورت كرة قدم", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("جوارب كرة قدم", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),

            // كرة السلة
            ("كرة سلة", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 68.00m) }),
            ("حذاء كرة سلة", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("قميص كرة سلة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),

            // كرة الطائرة
            ("كرة طائرة", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("شبكة كرة طائرة", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),

            // التنس
            ("مضرب تنس", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("كرة تنس", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("حذاء تنس", 165.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 205.00m) }),

            // السباحة
            ("نظارة سباحة", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("مايوه رجالي", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("مايوه نسائي", 55.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 68.00m) }),
            ("طوق سباحة", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),

            // الجري واللياقة
            ("حذاء جري", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("ملابس رياضية", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("حقيبة رياضية", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("ساعة رياضية", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),

            // أوزان ولياقة
            ("دمبل", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("حبل قفز", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),
            ("سجادة يوجا", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("كرة لياقة", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),

            // رياضات أخرى
            ("دراجة هوائية", 485.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 600.00m) }),
            ("خوذة دراجة", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("لوح تزلج", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("كرة بولينج", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف ألعاب الأطفال
    /// </summary>
    private async Task SeedToysItems(Category category, Unit pieceUnit, Unit boxUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // ألعاب الرضع
            ("خشخيشة", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("عضاضة", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("موبايل سرير", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("دمية ناعمة", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),

            // ألعاب تعليمية
            ("مكعبات ألوان", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 43.00m) }),
            ("أحجية خشبية", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 35.00m) }),
            ("لعبة أرقام", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 28.00m) }),
            ("لعبة حروف", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 32.00m) }),
            ("كتاب تلوين", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),

            // الدمى
            ("دمية باربي", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("دمية أطفال", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("دمية متحركة", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("بيت دمية", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),

            // السيارات والمركبات
            ("سيارة لعبة صغيرة", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),
            ("سيارة لعبة كبيرة", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("سيارة تحكم عن بعد", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("طائرة لعبة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("قطار لعبة", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 155.00m) }),

            // ألعاب البناء
            ("مكعبات ليجو", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 105.00m) }),
            ("مكعبات خشبية", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 55.00m) }),
            ("لعبة بناء", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 80.00m) }),

            // ألعاب إلكترونية
            ("جهاز ألعاب محمول", 285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 350.00m) }),
            ("لعبة إلكترونية", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("تابلت أطفال", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),

            // ألعاب خارجية
            ("أرجوحة", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("زحليقة", 285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 350.00m) }),
            ("ترامبولين", 485.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 600.00m) }),
            ("دراجة أطفال", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),

            // ألعاب فنية
            ("ألوان مائية", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 23.00m) }),
            ("أقلام تلوين", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 15.00m) }),
            ("صلصال", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 19.00m) }),
            ("لوحة رسم", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف متنوعة
    /// </summary>
    private async Task SeedMiscItems(Category category, Unit pieceUnit, Unit kiloUnit, Unit gramUnit, Unit literUnit, Unit mlUnit, Unit bottleUnit, Unit boxUnit, Unit bagUnit, Unit cartonUnit, Unit dozenUnit)
    {
        if (category == null) return;

        var items = new List<(string, decimal, bool, List<(Unit, decimal, bool, bool, decimal)>)>
        {
            // أدوات المطبخ
            ("طقم أواني طبخ", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("مقلاة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("قدر طبخ", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),
            ("طقم سكاكين", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("لوح تقطيع", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("مبشرة", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("مصفاة", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 28.00m) }),

            // أدوات المائدة
            ("طقم أطباق", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("طقم ملاعق", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("طقم أكواب", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("إبريق شاي", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 35.00m) }),
            ("إبريق قهوة", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),

            // أدوات التخزين
            ("علب حفظ طعام", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("برطمانات زجاج", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("أكياس تفريز", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 15.00m) }),
            ("ورق ألمنيوم", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("ورق تغليف طعام", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),

            // أدوات الحديقة
            ("مجرفة", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("خرطوم مياه", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("مقص حديقة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("بذور زهور", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("سماد نباتات", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 32.00m) }),

            // أدوات السيارة
            ("زيت محرك", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 55.00m) }),
            ("سائل تبريد", 28.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (literUnit, 1, true, false, 35.00m) }),
            ("منظف سيارة", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (bottleUnit, 1, true, false, 28.00m) }),
            ("إسفنج سيارة", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),

            // أدوات الإضاءة
            ("لمبة LED", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("لمبة فلورسنت", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),
            ("كشاف يدوي", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("شمعة", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 8.50m) }),

            // أدوات الصيانة
            ("مفك براغي", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),
            ("مطرقة", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("كماشة", 22.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 28.00m) }),
            ("شريط لاصق", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("غراء قوي", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 15.00m) }),

            // مستلزمات الحيوانات الأليفة
            ("طعام قطط", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 43.00m) }),
            ("طعام كلاب", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 55.00m) }),
            ("رمل قطط", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (kiloUnit, 1, true, false, 23.00m) }),
            ("لعبة حيوانات", 15.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 19.00m) }),

            // مستلزمات الحفلات
            ("بالونات", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (dozenUnit, 1, true, false, 11.00m) }),
            ("شموع عيد ميلاد", 6.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 8.50m) }),
            ("أطباق ورقية ملونة", 12.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 15.00m) }),
            ("أكواب ورقية", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (boxUnit, 1, true, false, 11.00m) }),

            // مستلزمات السفر
            ("حقيبة سفر", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("حقيبة يد", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("محفظة", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),
            ("جواز سفر حامل", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),

            // هدايا ومجوهرات
            ("ساعة يد", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("سلسلة ذهبية", 485.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 600.00m) }),
            ("خاتم", 185.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 230.00m) }),
            ("أقراط", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),

            // كتب ومجلات
            ("كتاب", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) }),
            ("مجلة", 8.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 11.00m) }),
            ("صحيفة", 2.50m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 3.20m) }),
            ("قاموس", 45.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 55.00m) }),

            // منتجات موسمية
            ("مروحة", 85.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 105.00m) }),
            ("دفاية", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("مظلة", 35.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 43.00m) }),
            ("نظارة شمسية", 65.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 80.00m) }),

            // منتجات تقنية متنوعة
            ("ساعة ذكية", 285.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 350.00m) }),
            ("سماعة بلوتوث صغيرة", 125.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 155.00m) }),
            ("حامل هاتف", 18.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 23.00m) }),
            ("كابل شحن سريع", 25.00m, false, new List<(Unit, decimal, bool, bool, decimal)> { (pieceUnit, 1, true, false, 32.00m) })
        };

        await CreateItemsWithUnits(items, category);
    }

    /// <summary>
    /// إضافة أصناف إضافية للوصول إلى 500 صنف
    /// </summary>
    private async Task SeedAdditionalItems(List<Category> categories, List<Unit> units)
    {
        try
        {
            Console.WriteLine("بدء إضافة الأصناف الإضافية...");

            // الحصول على عدد الأصناف الحالية
            var currentItemsCount = await _db.Items.CountAsync();
            Console.WriteLine($"عدد الأصناف الحالية: {currentItemsCount}");

            var targetCount = 500;
            var remainingItems = targetCount - currentItemsCount;

            if (remainingItems <= 0)
            {
                Console.WriteLine("تم الوصول إلى العدد المطلوب من الأصناف.");
                return;
            }

            Console.WriteLine($"سيتم إضافة {remainingItems} صنف إضافي.");

            // الوحدات الأساسية
            var pieceUnit = units.FirstOrDefault(u => u.Name == "قطعة");
            var kiloUnit = units.FirstOrDefault(u => u.Name == "كيلو");
            var gramUnit = units.FirstOrDefault(u => u.Name == "جرام");
            var literUnit = units.FirstOrDefault(u => u.Name == "لتر");
            var mlUnit = units.FirstOrDefault(u => u.Name == "مليلتر");
            var dozenUnit = units.FirstOrDefault(u => u.Name == "دستة");
            var cartonUnit = units.FirstOrDefault(u => u.Name == "كرتون");
            var boxUnit = units.FirstOrDefault(u => u.Name == "علبة");
            var bagUnit = units.FirstOrDefault(u => u.Name == "كيس");
            var bottleUnit = units.FirstOrDefault(u => u.Name == "زجاجة");

            // أسماء أصناف متنوعة
            var itemPrefixes = new[] { "منتج", "صنف", "مادة", "أداة", "جهاز", "مستلزم", "قطعة", "عبوة", "حاوية", "مجموعة" };
            var itemTypes = new[] { "غذائي", "منزلي", "طبي", "تقني", "رياضي", "تعليمي", "صناعي", "تجاري", "شخصي", "عام", "خاص", "محدود", "متميز", "فاخر", "اقتصادي" };
            var itemDescriptors = new[] { "ممتاز", "عالي الجودة", "مستورد", "محلي", "طبيعي", "صناعي", "متطور", "تقليدي", "حديث", "كلاسيكي", "مبتكر", "عملي", "أنيق", "قوي", "خفيف" };

            var batchSize = 100; // إضافة الأصناف على دفعات
            var addedCount = 0;

            while (addedCount < remainingItems)
            {
                var currentBatchSize = Math.Min(batchSize, remainingItems - addedCount);
                var batchItems = new List<Item>();

                for (int i = 0; i < currentBatchSize; i++)
                {
                    var itemIndex = addedCount + i + 1;
                    var category = categories[itemIndex % categories.Count];
                    var prefix = itemPrefixes[itemIndex % itemPrefixes.Length];
                    var type = itemTypes[itemIndex % itemTypes.Length];
                    var descriptor = itemDescriptors[itemIndex % itemDescriptors.Length];

                    var itemName = $"{prefix} {type} {descriptor} - رقم {itemIndex}";
                    var costPrice = Math.Round((decimal)(5 + (itemIndex % 500) * 0.5), 2);
                    var salePrice = Math.Round(costPrice * 1.25m, 2);
                    var hasExpiry = itemIndex % 3 == 0; // ثلث الأصناف لها تاريخ انتهاء

                    // إنشاء الصنف
                    var item = new Item
                    {
                        Id = Guid.NewGuid(),
                        Name = itemName,
                        CategoryId = category.Id,
                        CostPrice = costPrice,
                        IsHaveExp = hasExpiry,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    // إضافة وحدة قياس أساسية
                    var primaryUnit = (itemIndex % 4) switch
                    {
                        0 => pieceUnit,
                        1 => kiloUnit,
                        2 => literUnit,
                        _ => boxUnit
                    };

                    if (primaryUnit != null)
                    {
                        item.ItemUnits = new List<ItemUnit>
                        {
                            new ItemUnit
                            {
                                Id = Guid.NewGuid(),
                                ItemId = item.Id,
                                UnitId = primaryUnit.Id,
                                Quantity = 1,
                                IsBasicUnit = true,
                                IsBigger = false,
                                SalePrice = salePrice,
                                CreatedAt = DateTime.Now,
                                UpdatedAt = DateTime.Now
                            }
                        };
                    }

                    batchItems.Add(item);
                }

                // حفظ الدفعة الحالية
                _db.Items.AddRange(batchItems);
                await _db.SaveChangesAsync();

                addedCount += currentBatchSize;
                Console.WriteLine($"تم إضافة {addedCount} من {remainingItems} صنف إضافي...");
            }

            Console.WriteLine($"تم الانتهاء من إضافة {addedCount} صنف إضافي بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إضافة الأصناف الإضافية: {ex.Message}");
            throw;
        }
    }
}

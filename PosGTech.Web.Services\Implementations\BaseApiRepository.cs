using Microsoft.Extensions.Logging;
using Blazored.LocalStorage;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// Base class لجميع ApiRepositories مع إدارة JWT Token وmechanisms للتعامل مع 401 errors
    /// </summary>
    public abstract class BaseApiRepository : IDisposable
    {
        protected readonly HttpClient _httpClient;
        protected readonly ILogger _logger;
        private readonly ILocalStorageService _localStorage;

        // متغيرات إدارة JWT Token
        private string? _currentToken;
        private DateTime _tokenExpiry = DateTime.MinValue;
        private readonly SemaphoreSlim _tokenSemaphore = new(1, 1);
        private bool _tokenInitialized = false;

        protected BaseApiRepository(HttpClient httpClient, ILogger logger, ILocalStorageService localStorage)
        {
            _httpClient = httpClient;
            _logger = logger;
            _localStorage = localStorage;
        }

        /// <summary>
        /// ضمان إعداد Token قبل استخدام HttpClient
        /// </summary>
        private async Task EnsureTokenInitializedAsync()
        {
            // إعادة إعداد Token في كل مرة لضمان وجود Authorization Header
            await _tokenSemaphore.WaitAsync();
            try
            {
                await CheckAndSetTokenAsync();
                _tokenInitialized = true;
            }
            finally
            {
                _tokenSemaphore.Release();
            }
        }

        /// <summary>
        /// فحص وإعداد JWT Token في HttpClient
        /// </summary>
        private async Task CheckAndSetTokenAsync()
        {
            try
            {
                var token = await _localStorage.GetItemAsync<string>("authToken");
                _logger.LogInformation("🔍 Token من LocalStorage: {HasToken}", !string.IsNullOrEmpty(token));

                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("⚠️ لا يوجد توكن في Local Storage - يجب إعادة تسجيل الدخول");
                    throw new UnauthorizedAccessException("لا يوجد توكن صالح - يجب إعادة تسجيل الدخول");
                }

                var isValid = IsTokenValid(token);
                var expiry = GetTokenExpiry(token);
                _logger.LogInformation("✅ صلاحية Token: {IsValid}, انتهاء الصلاحية: {Expiry}", isValid, expiry);

                if (!isValid)
                {
                    _logger.LogWarning("⏰ التوكن منتهي الصلاحية، محاولة تجديد");
                    if (!await RefreshTokenIfNeededAsync())
                    {
                        _logger.LogError("❌ فشل في تجديد التوكن - يجب إعادة تسجيل الدخول");
                        await ClearTokenAsync();
                        throw new UnauthorizedAccessException("فشل في تجديد التوكن - يجب إعادة تسجيل الدخول");
                    }
                    token = await _localStorage.GetItemAsync<string>("authToken");
                    _logger.LogInformation("🔄 Token جديد بعد التجديد");
                }

                await SetAuthorizationHeaderAsync(token);
                _currentToken = token;
                _tokenExpiry = GetTokenExpiry(token);

                _logger.LogInformation("✅ تم إعداد التوكن بنجاح في HttpClient");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في إعداد التوكن");
                throw;
            }
        }

        /// <summary>
        /// إعداد Authorization Header في HttpClient
        /// </summary>
        private async Task SetAuthorizationHeaderAsync(string? token)
        {
            try
            {
                _logger.LogInformation("🔧 بدء إعداد Authorization Header");

                if (string.IsNullOrEmpty(token))
                {
                    _httpClient.DefaultRequestHeaders.Authorization = null;
                    _logger.LogWarning("⚠️ Token فارغ - تم إزالة Authorization Header");
                    return;
                }

                // استخدام Authorization property مباشرة - يضمن الإعداد الصحيح
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                // التحقق من إضافة Header بنجاح
                var authHeader = _httpClient.DefaultRequestHeaders.Authorization;
                var hasAuth = authHeader != null;

                _logger.LogInformation("✅ تم إعداد Authorization Header - موجود: {HasAuth}, النوع: {Scheme}",
                    hasAuth, authHeader?.Scheme);

                if (hasAuth && !string.IsNullOrEmpty(authHeader?.Parameter))
                {
                    var tokenPreview = authHeader.Parameter.Substring(0, Math.Min(50, authHeader.Parameter.Length)) + "...";
                    _logger.LogInformation("🎫 Token Preview: {TokenPreview}", tokenPreview);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في إعداد Authorization Header");
                throw;
            }
        }

        /// <summary>
        /// فحص صلاحية Token
        /// </summary>
        private bool IsTokenValid(string? token = null)
        {
            try
            {
                token ??= _currentToken;

                if (string.IsNullOrEmpty(token))
                    return false;

                var expiry = GetTokenExpiry(token);
                // تقليل هامش الأمان إلى دقيقة واحدة
                return expiry > DateTime.UtcNow.AddMinutes(1);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// استخراج تاريخ انتهاء Token
        /// </summary>
        private DateTime GetTokenExpiry(string token)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(token);
                return jsonToken.ValidTo;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// تجديد Token إذا كان قريب من انتهاء الصلاحية
        /// </summary>
        private async Task<bool> RefreshTokenIfNeededAsync()
        {
            try
            {
                var refreshToken = await _localStorage.GetItemAsync<string>("refreshToken");
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("لا يوجد Refresh Token");
                    return false;
                }

                _logger.LogInformation("محاولة تجديد التوكن");

                // إزالة Authorization Header قبل طلب التجديد
                _httpClient.DefaultRequestHeaders.Remove("Authorization");

                var refreshRequest = new
                {
                    RefreshToken = refreshToken
                };

                var response = await _httpClient.PostAsJsonAsync("Auth/refresh-token", refreshRequest);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<JsonElement>();

                    if (result.TryGetProperty("token", out var tokenElement) &&
                        result.TryGetProperty("refreshToken", out var newRefreshTokenElement))
                    {
                        var newToken = tokenElement.GetString();
                        var newRefreshToken = newRefreshTokenElement.GetString();

                        // تحديث LocalStorage
                        await _localStorage.SetItemAsync("authToken", newToken);
                        await _localStorage.SetItemAsync("refreshToken", newRefreshToken);

                        // إعادة إعداد Authorization Header
                        await SetAuthorizationHeaderAsync(newToken);

                        _logger.LogInformation("تم تجديد التوكن بنجاح");
                        return true;
                    }
                }

                _logger.LogWarning("فشل في تجديد التوكن: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تجديد التوكن");
                return false;
            }
        }

        /// <summary>
        /// معالجة استجابة 401 Unauthorized
        /// </summary>
        private async Task<bool> HandleUnauthorizedResponseAsync()
        {
            try
            {
                _logger.LogWarning("معالجة 401 Unauthorized");

                // إعادة تعيين حالة Token
                _tokenInitialized = false;
                _currentToken = null;
                _tokenExpiry = DateTime.MinValue;

                // محاولة تجديد Token
                if (await RefreshTokenIfNeededAsync())
                {
                    // إعادة إعداد Token الجديد
                    await CheckAndSetTokenAsync();
                    _tokenInitialized = true;
                    return true;
                }

                // إذا فشل التجديد، مسح جميع البيانات
                await ClearTokenAsync();
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة 401 Unauthorized");
                await ClearTokenAsync();
                return false;
            }
        }

        /// <summary>
        /// مسح Token من Local Storage وHttpClient
        /// </summary>
        private async Task ClearTokenAsync()
        {
            try
            {
                await _localStorage.RemoveItemAsync("authToken");
                await _localStorage.RemoveItemAsync("refreshToken");
                await _localStorage.RemoveItemAsync("expiresOn");

                _httpClient.DefaultRequestHeaders.Authorization = null;

                _currentToken = null;
                _tokenExpiry = DateTime.MinValue;
                _tokenInitialized = false;

                _logger.LogInformation("تم مسح التوكن من النظام");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مسح التوكن");
            }
        }

        /// <summary>
        /// إرسال طلب HTTP مع ضمان إعداد Token وmechanisms للتعامل مع 401 errors
        /// </summary>
        protected async Task<HttpResponseMessage> SendRequestAsync(Func<Task<HttpResponseMessage>> request)
        {
            // ضمان إعداد Token قبل كل طلب
            await EnsureTokenInitializedAsync();

            var response = await request();

            // معالجة 401 Unauthorized
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("تم استلام 401 Unauthorized، محاولة تجديد التوكن");

                if (await HandleUnauthorizedResponseAsync())
                {
                    _logger.LogInformation("تم تجديد التوكن بنجاح، إعادة المحاولة");
                    // إعادة المحاولة بعد تجديد Token
                    response = await request();
                }
                else
                {
                    _logger.LogError("فشل في تجديد التوكن، الطلب سيفشل");
                }
            }

            return response;
        }

        /// <summary>
        /// إرسال طلب GET مع إدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> GetAsync(string requestUri)
        {
            // SendRequestAsync ستتولى إعداد Token تلقائياً
            return await SendRequestAsync(() => _httpClient.GetAsync(requestUri));
        }

        /// <summary>
        /// إرسال طلب POST مع إدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> PostAsJsonAsync<T>(string requestUri, T value)
        {
            return await SendRequestAsync(() => _httpClient.PostAsJsonAsync(requestUri, value));
        }

        /// <summary>
        /// إرسال طلب POST مع StringContent وإدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> PostAsync(string requestUri, StringContent content)
        {
            return await SendRequestAsync(() => _httpClient.PostAsync(requestUri, content));
        }

        /// <summary>
        /// إرسال طلب POST مع HttpContent وإدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> PostAsync(string requestUri, HttpContent content)
        {
            return await SendRequestAsync(() => _httpClient.PostAsync(requestUri, content));
        }

        /// <summary>
        /// إرسال طلب PUT مع إدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> PutAsJsonAsync<T>(string requestUri, T value)
        {
            return await SendRequestAsync(() => _httpClient.PutAsJsonAsync(requestUri, value));
        }

        /// <summary>
        /// إرسال طلب PUT مع HttpContent وإدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> PutAsync(string requestUri, HttpContent content)
        {
            return await SendRequestAsync(() => _httpClient.PutAsync(requestUri, content));
        }

        /// <summary>
        /// إرسال طلب DELETE مع إدارة Token
        /// </summary>
        protected async Task<HttpResponseMessage> DeleteAsync(string requestUri)
        {
            return await SendRequestAsync(() => _httpClient.DeleteAsync(requestUri));
        }

        /// <summary>
        /// Helper method لتحويل object إلى StringContent
        /// </summary>
        protected StringContent CreateJsonContent<T>(T obj)
        {
            var json = JsonSerializer.Serialize(obj);
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _tokenSemaphore?.Dispose();
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}

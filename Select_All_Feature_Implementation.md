# ميزة تحديد النص بالكامل (Select All) في فاتورة الشراء

## نظرة عامة
تم إضافة ميزة "تحديد الكل" (Select All) لتحسين تجربة الكتابة في حقول النموذج في صفحة فاتورة الشراء. هذه الميزة تسمح للمستخدم بتحديد النص الموجود بالكامل تلقائياً عند التركيز على أي حقل نصي أو رقمي.

## الحقول المشمولة

### 1. حقل رقم الفاتورة (InvoiceNo)
- **النوع**: `MudTextField<int>`
- **الوظيفة**: `SelectAllInvoiceNo()`
- **السلوك**: تحديد رقم الفاتورة بالكامل عند التركيز

### 2. حقل البحث عن الصنف (ItemForAdd)
- **النوع**: `MudAutocomplete<ItemCMDTO>`
- **الوظيفة**: `SelectAllItemSearch()`
- **السلوك**: تحديد نص البحث بالكامل عند التركيز

### 3. حقل الكمية (QuantityField)
- **النوع**: `MudNumericField<decimal>`
- **الوظيفة**: `SelectAllQuantity()`
- **السلوك**: تحديد قيمة الكمية بالكامل عند التركيز

### 4. حقل سعر الشراء (PurchasePriceField)
- **النوع**: `MudNumericField<decimal>`
- **الوظيفة**: `SelectAllPurchasePrice()`
- **السلوك**: تحديد سعر الشراء بالكامل عند التركيز

### 5. حقل سعر البيع (SalePriceField)
- **النوع**: `MudNumericField<decimal>`
- **الوظيفة**: `SelectAllSalePrice()`
- **السلوك**: تحديد سعر البيع بالكامل عند التركيز

### 6. حقل التخفيض (DiscountField)
- **النوع**: `MudTextField<decimal>`
- **الوظيفة**: `SelectAllDiscount()`
- **السلوك**: تحديد قيمة التخفيض بالكامل عند التركيز

### 7. حقل المدفوع (PaidField)
- **النوع**: `MudNumericField<decimal>`
- **الوظيفة**: `SelectAllPaid()`
- **السلوك**: تحديد المبلغ المدفوع بالكامل عند التركيز

## التنفيذ التقني

### في الكود الخلفي (UpsertPurchase.razor.cs)

#### إضافة المراجع الجديدة:
```csharp
// مراجع إضافية للحقول التي تحتاج تحديد الكل
MudTextField<decimal> DiscountField { get; set; }
MudNumericField<decimal> PaidField { get; set; }
```

#### دوال تحديد النص:
```csharp
#region دوال تحديد النص بالكامل (Select All)

/// <summary>
/// تحديد النص بالكامل في حقل رقم الفاتورة
/// </summary>
private async Task SelectAllInvoiceNo()
{
    if (InvoiceNo != null)
    {
        await Task.Delay(10); // تأخير قصير للسماح بالتركيز
        await InvoiceNo.SelectAsync();
    }
}

/// <summary>
/// تحديد النص بالكامل في حقل البحث عن الصنف
/// </summary>
private async Task SelectAllItemSearch()
{
    if (ItemForAdd != null)
    {
        await Task.Delay(10); // تأخير قصير للسماح بالتركيز
        await ItemForAdd.SelectAllAsync();
    }
}

// ... باقي الدوال
#endregion
```

### في الواجهة (UpsertPurchase.razor)

#### مثال على التحديث:
```razor
<!-- حقل رقم الفاتورة -->
<MudTextField T="int" Value="purchase.InvoiceNo" @ref="InvoiceNo"
              Label="رقم الفاتورة" OnKeyDown="KeyDownInvoice"
              ValueChanged="@((int value) => { purchase.InvoiceNo = value; ValidateInvoiceNumber(); })"
              Variant="Variant.Outlined"
              AdornmentIcon="@Icons.Material.Filled.Numbers"
              Adornment="Adornment.End"
              For="@(() => purchase.InvoiceNo)"
              Class="invoice-field flex-grow-1 compact-field"
              Margin="Margin.Dense"
              @onfocus="SelectAllInvoiceNo" />
```

## المزايا

### 1. تحسين تجربة المستخدم
- **سرعة في التعديل**: لا حاجة لتحديد النص يدوياً
- **سهولة الاستخدام**: تحديد تلقائي عند التركيز
- **تقليل الأخطاء**: تجنب الكتابة بجانب النص الموجود

### 2. كفاءة في الإدخال
- **توفير الوقت**: تحديد فوري للنص
- **تدفق طبيعي**: يتماشى مع توقعات المستخدم
- **تجربة متسقة**: نفس السلوك في جميع الحقول

### 3. التوافق مع النظام الموجود
- **عدم التداخل**: لا يؤثر على نظام التنقل بـ Enter
- **الحفاظ على الوظائف**: جميع الوظائف الموجودة تعمل كما هي
- **تحسين إضافي**: ميزة إضافية بدون تعديل الوظائف الأساسية

## السلوك المتوقع

### عند التركيز على الحقل:
1. **بالنقر**: تحديد النص بالكامل تلقائياً
2. **بالتنقل (Tab/Enter)**: تحديد النص بالكامل تلقائياً
3. **بالبرمجة**: تحديد النص عند استدعاء `FocusAsync()`

### عند البدء بالكتابة:
1. **استبدال النص**: النص الجديد يحل محل النص المحدد
2. **عدم الإضافة**: لا يتم إضافة النص الجديد للنص الموجود
3. **تحديث فوري**: تحديث القيمة في النموذج مباشرة

### التوافق مع الوظائف الموجودة:
1. **التنقل بـ Enter**: يعمل كما هو بدون تأثير
2. **التحقق من البيانات**: يعمل كما هو
3. **الحفظ والتحديث**: يعمل كما هو

## اختبار الميزة

### خطوات الاختبار الأساسية:
1. فتح صفحة إنشاء فاتورة شراء جديدة
2. النقر على أي حقل من الحقول المشمولة
3. التحقق من تحديد النص بالكامل
4. كتابة نص جديد والتحقق من الاستبدال
5. استخدام Tab/Enter للتنقل والتحقق من التحديد

### حالات اختبار متقدمة:
1. **الحقول الفارغة**: التحقق من عدم حدوث أخطاء
2. **الحقول المعطلة**: التحقق من عدم التأثير
3. **التنقل السريع**: التحقق من عمل التحديد مع التنقل السريع
4. **القيم الكبيرة**: التحقق من تحديد النصوص الطويلة

## الصيانة والتطوير

### إضافة حقول جديدة:
1. إضافة مرجع للحقل في الكود الخلفي
2. إنشاء دالة تحديد مخصصة
3. إضافة `@onfocus` في الواجهة
4. اختبار الوظيفة

### تخصيص السلوك:
- تعديل التأخير في `Task.Delay(10)`
- إضافة شروط خاصة للتحديد
- تخصيص السلوك حسب نوع الحقل

## الخلاصة
تم تنفيذ ميزة "تحديد الكل" بنجاح في جميع الحقول المطلوبة، مما يحسن تجربة المستخدم ويزيد من كفاءة إدخال البيانات دون التأثير على الوظائف الموجودة.

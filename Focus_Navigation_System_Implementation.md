# نظام التنقل بالتركيز والانتقال بزر Enter - صفحة فواتير المشتريات

## نظرة عامة
تم تنفيذ نظام شامل للتنقل بالتركيز (Focus Navigation) والانتقال بزر Enter في صفحة فواتير المشتريات (`UpsertPurchase.razor`). يسمح هذا النظام للمستخدمين بالتنقل بسرعة وكفاءة بين الحقول باستخدام زر Enter.

## الميزات المنفذة

### 1. تسلسل التركيز (Tab Order)
تم إعداد تسلسل محدد للحقول بالترتيب التالي:
1. **حقل المخازن** (Store/Warehouse field) - TabIndex: 1
2. **حقل المورد** (Supplier field) - TabIndex: 2
3. **حقل الصنف** (Item field) - TabIndex: 3
4. **حقل الوحدة** (Unit field) - TabIndex: 4
5. **حقل تاريخ الصلاحية** (Expiry Date field) - TabIndex: 5
6. **حقل الكمية** (Quantity field) - TabIndex: 6
7. **حقل سعر الشراء** (Purchase Price field) - TabIndex: 7
8. **حقل سعر البيع** (Sale Price field) - TabIndex: 8
9. **زر إضافة الصنف** (Add Item button) - TabIndex: 9

### 2. وظائف التنقل
- **الضغط على زر Enter**: ينقل التركيز للحقل التالي في التسلسل
- **زر إضافة الصنف**: عند الضغط على Enter، يتم تنفيذ إضافة الصنف
- **العودة التلقائية**: بعد إضافة الصنف، يعود التركيز تلقائياً لحقل الصنف

### 3. السلوك الذكي
- **تخطي الحقول الاختيارية**: يتم تخطي تاريخ الصلاحية إذا لم يكن مطلوباً للصنف المحدد
- **التحقق من صحة البيانات**: يتم التحقق من صحة الحقل الحالي قبل الانتقال
- **منع الانتقال**: إذا كان الحقل الحالي يحتوي على خطأ في التحقق

## التفاصيل التقنية

### الملفات المعدلة

#### 1. UpsertPurchase.razor.cs
**المراجع المضافة:**
```csharp
// مراجع الحقول للتنقل بالتركيز
MudSelect<Guid?> StoreField { get; set; }
MudComboBox<ClientCMDTO> SupplierField { get; set; }
MudDatePicker ExpiryDateField { get; set; }
MudNumericField<decimal> QuantityField { get; set; }
MudNumericField<decimal> PurchasePriceField { get; set; }
MudNumericField<decimal> SalePriceField { get; set; }
MudButton AddItemButton { get; set; }
```

**المتغيرات المضافة:**
```csharp
// متغيرات التنقل بالتركيز
private int _currentTabIndex = 1;
private readonly Dictionary<int, Func<Task>> _focusActions = new();
```

**الدوال الرئيسية:**
- `InitializeFocusActions()`: إعداد تسلسل التركيز للحقول
- `HandleEnterKeyPress()`: معالج الضغط على زر Enter للانتقال للحقل التالي
- `MoveToNextField()`: الانتقال للحقل التالي في التسلسل
- `GetNextTabIndex()`: الحصول على فهرس الحقل التالي مع تخطي الحقول غير المطلوبة
- `ValidateCurrentField()`: التحقق من صحة الحقل الحالي
- `IsExpiryDateRequired()`: التحقق من ضرورة تاريخ الصلاحية للصنف المحدد

**دوال التركيز:**
- `FocusStoreField()`, `FocusSupplierField()`, `FocusItemField()`, إلخ.

#### 2. UpsertPurchase.razor
**التحديثات المطبقة:**
- إضافة `@ref` attributes لجميع الحقول
- إضافة `TabIndex` properties بالترتيب المحدد
- إضافة `OnKeyDown` event handlers لكل حقل
- تحديث دالة `AddItemPurchase` لتصبح async Task

### منطق التحقق من صحة البيانات

```csharp
private async Task<bool> ValidateCurrentField(int tabIndex)
{
    switch (tabIndex)
    {
        case 1: // المخزن
            if (purchase.StoreId == null)
            {
                ShowErrorMessage("يجب اختيار المخزن");
                return false;
            }
            break;
        case 3: // الصنف
            if (selectedPurchaseItem.Item == null)
            {
                ShowErrorMessage("يجب اختيار الصنف");
                return false;
            }
            break;
        // ... المزيد من التحققات
    }
    return true;
}
```

### منطق تخطي الحقول الاختيارية

```csharp
private int GetNextTabIndex(int currentTabIndex)
{
    var nextIndex = currentTabIndex + 1;
    
    // تخطي تاريخ الصلاحية إذا لم يكن مطلوباً للصنف المحدد
    if (nextIndex == 5 && !IsExpiryDateRequired())
    {
        nextIndex = 6;
    }
    
    // إذا وصلنا لنهاية التسلسل، ابدأ من جديد
    if (nextIndex > 9)
    {
        nextIndex = 3; // العودة لحقل الصنف
    }
    
    return nextIndex;
}
```

## فوائد النظام

### 1. تحسين تجربة المستخدم
- **سرعة الإدخال**: التنقل السريع بين الحقول دون استخدام الماوس
- **سهولة الاستخدام**: تدفق طبيعي ومنطقي للبيانات
- **تقليل الأخطاء**: التحقق من صحة البيانات قبل الانتقال

### 2. الكفاءة التشغيلية
- **توفير الوقت**: تقليل الوقت المطلوب لإدخال فاتورة مشتريات
- **تقليل الحركة**: عدم الحاجة للتنقل بالماوس بين الحقول
- **تدفق مستمر**: عدم انقطاع عملية الإدخال

### 3. المرونة
- **تخطي ذكي**: تخطي الحقول غير المطلوبة تلقائياً
- **تحقق مرن**: التحقق من صحة البيانات حسب نوع الحقل
- **عودة تلقائية**: العودة لحقل الصنف بعد إضافة كل صنف

## الاستخدام

### للمستخدم النهائي:
1. ابدأ بحقل المخازن
2. اضغط Enter للانتقال للحقل التالي
3. املأ البيانات المطلوبة في كل حقل
4. عند الوصول لزر "إضافة الصنف"، اضغط Enter لإضافة الصنف
5. سيعود التركيز تلقائياً لحقل الصنف لإدخال صنف جديد

### للمطورين:
- النظام قابل للتوسيع لإضافة حقول جديدة
- يمكن تخصيص منطق التحقق لكل حقل
- يمكن تعديل تسلسل التركيز حسب الحاجة

## ملاحظات مهمة

1. **التوافق**: النظام متوافق مع جميع مكونات MudBlazor المستخدمة
2. **الأداء**: لا يؤثر على أداء الصفحة أو سرعة التحميل
3. **الصيانة**: الكود منظم ومقسم إلى regions للسهولة في الصيانة
4. **التوسعة**: يمكن تطبيق نفس النظام على صفحات أخرى في التطبيق

## الخطوات التالية المقترحة

1. **اختبار شامل**: اختبار النظام مع جميع سيناريوهات الاستخدام
2. **تحسينات إضافية**: إضافة shortcuts أخرى (مثل Ctrl+S للحفظ)
3. **تطبيق على صفحات أخرى**: تطبيق نفس النظام على صفحات المبيعات والمرتجعات
4. **تخصيص المستخدم**: السماح للمستخدم بتخصيص تسلسل التركيز

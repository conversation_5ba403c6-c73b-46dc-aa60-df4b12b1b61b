# دليل اختبار ميزة تحديد النص بالكامل (Select All)

## نظرة عامة
هذا الدليل يوضح كيفية اختبار ميزة "تحديد الكل" المضافة حديثاً في صفحة فاتورة الشراء.

## الحقول المشمولة في الاختبار

### ✅ الحقول المحدثة:
1. **حقل رقم الفاتورة** - `InvoiceNo`
2. **حقل البحث عن الصنف** - `ItemForAdd`
3. **حقل الكمية** - `QuantityField`
4. **حقل سعر الشراء** - `PurchasePriceField`
5. **حقل سعر البيع** - `SalePriceField`
6. **حقل التخفيض** - `DiscountField`
7. **حقل المدفوع** - `PaidField`

## خطوات الاختبار

### 🧪 الاختبار الأساسي

#### 1. اختبار حقل رقم الفاتورة
```
الخطوات:
1. فتح صفحة إنشاء فاتورة شراء جديدة
2. النقر على حقل "رقم الفاتورة"
3. التحقق من تحديد الرقم بالكامل
4. كتابة رقم جديد والتحقق من الاستبدال

النتيجة المتوقعة:
- تحديد الرقم الموجود بالكامل عند التركيز
- استبدال الرقم عند الكتابة
```

#### 2. اختبار حقل البحث عن الصنف
```
الخطوات:
1. الانتقال لحقل "الصنف"
2. كتابة اسم صنف جزئي
3. النقر خارج الحقل ثم العودة إليه
4. التحقق من تحديد النص بالكامل

النتيجة المتوقعة:
- تحديد النص الموجود بالكامل عند التركيز
- إمكانية الكتابة فوق النص المحدد
```

#### 3. اختبار الحقول الرقمية
```
الحقول: الكمية، سعر الشراء، سعر البيع، المدفوع

الخطوات لكل حقل:
1. إدخال قيمة رقمية
2. النقر خارج الحقل
3. العودة للحقل بالنقر أو Tab
4. التحقق من تحديد القيمة بالكامل
5. كتابة قيمة جديدة

النتيجة المتوقعة:
- تحديد القيمة الرقمية بالكامل
- استبدال القيمة عند الكتابة
```

#### 4. اختبار حقل التخفيض
```
الخطوات:
1. الانتقال لقسم "الإجماليات والدفع"
2. النقر على حقل "التخفيض"
3. التحقق من تحديد القيمة بالكامل
4. إدخال قيمة جديدة

النتيجة المتوقعة:
- تحديد قيمة التخفيض بالكامل
- تحديث الإجماليات عند التغيير
```

### 🔄 اختبار التنقل

#### 1. التنقل بـ Tab
```
الخطوات:
1. البدء من حقل رقم الفاتورة
2. استخدام Tab للتنقل بين الحقول
3. التحقق من تحديد النص في كل حقل عند الوصول إليه

النتيجة المتوقعة:
- تحديد النص تلقائياً عند الوصول بـ Tab
- عدم تداخل مع نظام التنقل الموجود
```

#### 2. التنقل بـ Enter
```
الخطوات:
1. إدخال بيانات في حقل الصنف
2. الضغط على Enter
3. التحقق من الانتقال للحقل التالي
4. التحقق من تحديد النص في الحقل الجديد

النتيجة المتوقعة:
- الانتقال الصحيح للحقل التالي
- تحديد النص في الحقل الجديد
- عدم تأثير على وظائف Enter الموجودة
```

### ⚠️ اختبار الحالات الخاصة

#### 1. الحقول الفارغة
```
الخطوات:
1. التركيز على حقل فارغ
2. التحقق من عدم حدوث أخطاء
3. كتابة نص جديد

النتيجة المتوقعة:
- عدم حدوث أخطاء
- إمكانية الكتابة بشكل طبيعي
```

#### 2. الحقول المعطلة
```
الخطوات:
1. اختبار حقل "المدفوع" في فاتورة موجودة (معطل)
2. محاولة التركيز عليه
3. التحقق من عدم تأثير الميزة

النتيجة المتوقعة:
- عدم تأثير على الحقول المعطلة
- عدم حدوث أخطاء
```

#### 3. القيم الكبيرة
```
الخطوات:
1. إدخال أرقام كبيرة في الحقول الرقمية
2. التحقق من تحديد القيم الطويلة
3. اختبار الاستبدال

النتيجة المتوقعة:
- تحديد القيم الطويلة بالكامل
- عدم مشاكل في الأداء
```

### 🚀 اختبار الأداء

#### 1. التنقل السريع
```
الخطوات:
1. التنقل السريع بين الحقول باستخدام Tab
2. التحقق من استجابة التحديد
3. اختبار عدة مرات متتالية

النتيجة المتوقعة:
- استجابة سريعة للتحديد
- عدم تأخير ملحوظ
```

#### 2. الاستخدام المكثف
```
الخطوات:
1. إنشاء عدة فواتير متتالية
2. استخدام الميزة في كل فاتورة
3. مراقبة الأداء والذاكرة

النتيجة المتوقعة:
- أداء ثابت
- عدم تسريب في الذاكرة
```

## ✅ قائمة التحقق النهائية

### الوظائف الأساسية:
- [ ] تحديد النص في حقل رقم الفاتورة
- [ ] تحديد النص في حقل البحث عن الصنف
- [ ] تحديد النص في حقل الكمية
- [ ] تحديد النص في حقل سعر الشراء
- [ ] تحديد النص في حقل سعر البيع
- [ ] تحديد النص في حقل التخفيض
- [ ] تحديد النص في حقل المدفوع

### التوافق:
- [ ] التنقل بـ Tab يعمل بشكل صحيح
- [ ] التنقل بـ Enter يعمل بشكل صحيح
- [ ] وظائف الحفظ والتحديث تعمل
- [ ] التحقق من البيانات يعمل
- [ ] لا توجد أخطاء في وحدة التحكم

### تجربة المستخدم:
- [ ] سهولة الاستخدام
- [ ] استجابة سريعة
- [ ] سلوك متوقع ومنطقي
- [ ] عدم تداخل مع الوظائف الموجودة

## 🐛 الأخطاء المحتملة وحلولها

### مشكلة: عدم تحديد النص
**الحل**: التحقق من وجود المرجع وصحة الطريقة المستخدمة

### مشكلة: تداخل مع التنقل
**الحل**: التحقق من عدم تعارض مع معالجات الأحداث الموجودة

### مشكلة: بطء في الاستجابة
**الحل**: تقليل التأخير في `Task.Delay(10)` أو إزالته

## 📝 تقرير الاختبار

```
تاريخ الاختبار: ___________
المختبر: ___________
إصدار النظام: ___________

النتائج:
- الوظائف الأساسية: ___/7
- التوافق: ___/5  
- تجربة المستخدم: ___/4

المشاكل المكتشفة:
_________________________________
_________________________________

التوصيات:
_________________________________
_________________________________

الحالة النهائية: [ ] مقبول [ ] يحتاج تحسين [ ] مرفوض
```

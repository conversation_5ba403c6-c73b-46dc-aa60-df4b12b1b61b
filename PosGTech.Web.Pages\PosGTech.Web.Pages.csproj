﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Pages\Users\UpsertUserCSS.razor" />
  </ItemGroup>


  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="CodeBeam.MudBlazor.Extensions" Version="7.1.0" />
    <PackageReference Include="IKVM" Version="8.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.12" />
    <PackageReference Include="MudBlazor" Version="7.15.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PosGTech.ModelsDTO\PosGTech.ModelsDTO.csproj" />
    <ProjectReference Include="..\PosGTech.Web.Services\PosGTech.Web.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Pages\Authentication\Login.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Categories\ListCategories.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Categories\UpsertCategory.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Clients\ListClients.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Clients\UpsertClient.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Employees\ListEmployees.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Employees\UpsertEmployee.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Expenses\ListExpenses.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Expenses\UpsertExpense.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Home.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Inventories\ListInventory.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Inventories\UpsertInventory.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Items\ListItems.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Items\UpsertItem.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Purchases\ListPurchase.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Purchases\UpsertPurchase.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\ListReceipts.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertFirstReceiptClients.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertFirstReceiptEmployees.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceipt.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptClients.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptEmployees.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptExpenses.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptPurchases.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptSalaryEmployees.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptSells.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertReceiptTreasury.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Receipts\UpsertWithdrawDepositReceiptTreasury.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Sells\UpsertSell.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Stores\ListStores.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Stores\UpsertStore.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Treasuriess\UpsertTreasuries.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Treasuriess\ViewTreasuries.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Units\ListUnits.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Units\UpsertUnit.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Users\ListUsers.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="Pages\Users\UpsertUser.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Update="_Imports.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\" />
  </ItemGroup>

</Project>

﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authorization;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Users
{
    public partial class ListUsers
    {
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }

        [Inject]
        AuthenticationStateProvider _auth { get; set; }
        IEnumerable<UserDTO> users = new List<UserDTO>();
        UserDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameUserForDelete;
        Guid? currentUserId = null;
        MudMessageBox mbox { get; set; }


        protected override async Task OnInitializedAsync()
        {
            // الحصول على معرف المستخدم الحالي
            var auth = await _auth.GetAuthenticationStateAsync();
            var userIdClaim = auth.User.Claims.FirstOrDefault(x => x.Type == "id")?.Value;
            if (!string.IsNullOrEmpty(userIdClaim) && Guid.TryParse(userIdClaim, out var userId))
            {
                currentUserId = userId;
            }

            await LoadingData();
        }
        private bool FilterFunc1(UserDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(UserDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        void Upsert(Guid id)
        {
            _navigation.NavigateTo($"/upsertUser/{id}");
        }
        async void Delete(UserDTO obj)
        {
            NameUserForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.User.DeleteUserAsync(obj.Id!.Value.ToString());
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }

        async void Restore(UserDTO obj)
        {
            NameUserForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                // TODO: إضافة طريقة RestoreUser إلى UserApiRepository
                _snackbar.Add("وظيفة استعادة المستخدم قيد التطوير", Severity.Info);
            }
        }

        bool IsUserActive(UserDTO user)
        {
            // المستخدم نشط إذا كان LockoutEnabled = true و (LockoutEnd = null أو LockoutEnd في الماضي)
            return user.State;
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();

            Console.WriteLine("=== LoadingData START ===");
            var res = await _unitOfWork.User.GetAllUsersAsync();

            if (res.response == null)
            {
                users = res.list ?? new List<UserDTO>();
                Console.WriteLine($"Loaded {users.Count()} users");

                // تشخيص للمستخدمين المحملين
                foreach (var user in users.Take(3)) // أول 3 مستخدمين فقط
                {
                    Console.WriteLine($"User: {user.Name}");
                    Console.WriteLine($"  Role: {user.RoleName}");
                    Console.WriteLine($"  Additional Permissions: {user.AdditionalPermissions?.Count ?? 0}");
                    Console.WriteLine($"  Removed Permissions: {user.RemovedPermissions?.Count ?? 0}");
                    Console.WriteLine($"  Effective Permissions: {user.EffectivePermissions?.Count ?? 0}");
                }
            }
            else
            {
                Console.WriteLine($"Error loading users: {res.response.Message}");
                _snackbar.Add(res.response.Message, Severity.Error);
            }

            loading = false;
            StateHasChanged();
            Console.WriteLine("=== LoadingData END ===");
        }

        bool CanDeleteUser(UserDTO user)
        {
            // لا يمكن حذف الأدمن الرئيسي
            if (user.UserName == "Admin")
                return false;

            // لا يمكن للمستخدم حذف نفسه إذا كان أدمن رئيسي
            if (currentUserId.HasValue && currentUserId.Value == user.Id)
            {
                var currentUser = users.FirstOrDefault(u => u.Id == currentUserId.Value);
                if (currentUser != null && currentUser.UserName == "Admin")
                    return false;
            }

            return true;
        }



    }
}
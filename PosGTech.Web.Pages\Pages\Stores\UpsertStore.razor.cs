﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Stores
{
    public partial class UpsertStore
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        StoreDTO Store = new();
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _unitOfWork.Store.GetStoreByIdAsync(id);
                if (res.response == null)
                {
                    Store = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }


            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _unitOfWork.Store.InsertStoreAsync(Store);
            else
                response = await _unitOfWork.Store.UpdateStoreAsync(id, Store);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

    }
}
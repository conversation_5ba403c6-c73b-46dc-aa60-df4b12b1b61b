using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Inventories;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IInventoryApiRepository
    {
        /// <summary>
        /// جلب جميع الجرد
        /// </summary>
        Task<(IEnumerable<InventoryDTO>? list, ResponseVM? response)> GetAllInventoriesAsync();

        /// <summary>
        /// جلب جرد بالمعرف
        /// </summary>
        Task<(InventoryDTO? model, ResponseVM? response)> GetInventoryByIdAsync(Guid id);

        /// <summary>
        /// إضافة جرد جديد
        /// </summary>
        Task<ResponseVM> InsertInventoryAsync(InventoryDTO inventory);

        /// <summary>
        /// تحديث جرد موجود
        /// </summary>
        Task<ResponseVM> UpdateInventoryAsync(Guid id, InventoryDTO inventory);

        /// <summary>
        /// حذف جرد
        /// </summary>
        Task<ResponseVM> DeleteInventoryAsync(Guid id);

        /// <summary>
        /// جلب عناصر الجرد
        /// </summary>
        Task<(IEnumerable<InventoryItemDTO>? list, ResponseVM? response)> GetInventoryItemsAsync(Guid inventoryId);
    }
}

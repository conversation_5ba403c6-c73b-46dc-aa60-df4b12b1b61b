﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Units;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Units
{
    public partial class UpsertUnit
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        UnitDTO Unit = new();
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _unitOfWork.Unit.GetUnitByIdAsync(id);
                if (res.response == null)
                {
                    Unit = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }


            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _unitOfWork.Unit.InsertUnitAsync(Unit);
            else
                response = await _unitOfWork.Unit.UpdateUnitAsync(id, Unit);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

    }
}
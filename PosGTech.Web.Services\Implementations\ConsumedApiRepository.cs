using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Consumeds;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمستهلكات - يحتوي على جميع العمليات المطلوبة للتعامل مع المستهلكات
    /// </summary>
    public class ConsumedApiRepository : BaseApiRepository, IConsumedApiRepository
    {
        public ConsumedApiRepository(HttpClient httpClient, ILogger<ConsumedApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع المستهلكات
        /// </summary>
        public async Task<(IEnumerable<ConsumedDTO>? list, ResponseVM? response)> GetAllConsumedsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المستهلكات");
                var response = await GetAsync("Consumeds/getAllConsumeds");

                if (response.IsSuccessStatusCode)
                {
                    var consumeds = await response.Content.ReadFromJsonAsync<ConsumedDTO[]>();
                    return (consumeds, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المستهلكات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب مستهلك بالمعرف
        /// </summary>
        public async Task<(ConsumedDTO? model, ResponseVM? response)> GetConsumedByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب المستهلك بالمعرف: {Id}", id);
                var response = await GetAsync($"Consumeds/getConsumedById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var consumed = await response.Content.ReadFromJsonAsync<ConsumedDTO>();
                    return (consumed, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المستهلك بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة مستهلك جديد
        /// </summary>
        public async Task<ResponseVM> InsertConsumedAsync(ConsumedDTO consumed)
        {
            try
            {
                _logger.LogInformation("إضافة مستهلك جديد");
                var response = await PostAsJsonAsync("Consumeds/insertConsumed", consumed);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة المستهلك بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المستهلك");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث مستهلك موجود
        /// </summary>
        public async Task<ResponseVM> UpdateConsumedAsync(Guid id, ConsumedDTO consumed)
        {
            try
            {
                _logger.LogInformation("تحديث المستهلك: {Id}", id);
                var response = await PutAsJsonAsync($"Consumeds/updateConsumed/{id}", consumed);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث المستهلك بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المستهلك: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف مستهلك
        /// </summary>
        public async Task<ResponseVM> DeleteConsumedAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف المستهلك: {Id}", id);
                var response = await DeleteAsync($"Consumeds/deleteConsumed/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف المستهلك بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المستهلك: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب عناصر المستهلك
        /// </summary>
        public async Task<(IEnumerable<ConsumedItemDTO>? list, ResponseVM? response)> GetConsumedItemsAsync(Guid consumedId)
        {
            try
            {
                _logger.LogInformation("جلب عناصر المستهلك: {ConsumedId}", consumedId);
                var response = await GetAsync($"Consumeds/getConsumedItems/{consumedId}");

                if (response.IsSuccessStatusCode)
                {
                    var consumedItems = await response.Content.ReadFromJsonAsync<ConsumedItemDTO[]>();
                    return (consumedItems, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عناصر المستهلك: {ConsumedId}", consumedId);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}

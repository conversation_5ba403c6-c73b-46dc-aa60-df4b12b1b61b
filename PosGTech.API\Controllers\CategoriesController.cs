﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Categories;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CategoriesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllCategories")]
        public async Task<IActionResult> GetAllCategories()
        {
            var categories = maper.Map<IEnumerable<Category>, IEnumerable<CategoryDTO>>(await unitOfWork.Category.GetAll());
            return Ok(categories);
        }


        [HttpGet("getCategoryById/{id:Guid}")]
        public async Task<IActionResult> GetCategoryById([FromRoute] Guid id)
        {
            var category = maper.Map<Category, CategoryDTO>(await unitOfWork.Category.GetByIdAsync(id));
            return Ok(category);
        }



        [HttpPost("insertCategory")]
        public async Task<IActionResult> InsertCategory([FromBody] CategoryDTO model)
        {
            var newcategory = maper.Map<CategoryDTO, Category>(model);
            unitOfWork.Category.Add(newcategory);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateCategory/{id:Guid}")]
        public async Task<IActionResult> UpdateCategory([FromRoute] Guid id, CategoryDTO model)
        {
            var oldcategory = await unitOfWork.Category.GetByIdAsync(id);
            if (oldcategory == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            unitOfWork.Category.UpdateCategory(oldcategory, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteCategory/{id:Guid}")]
        public async Task<IActionResult> DeleteCategory([FromRoute] Guid id)
        {
            var oldcategory = await unitOfWork.Category.GetByIdAsync(id);
            if (oldcategory == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Category.Remove(oldcategory);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}

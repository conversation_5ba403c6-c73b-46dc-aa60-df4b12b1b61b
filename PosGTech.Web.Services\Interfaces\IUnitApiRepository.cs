using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Units;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IUnitApiRepository
    {
        /// <summary>
        /// جلب جميع الوحدات
        /// </summary>
        Task<(IEnumerable<UnitDTO>? list, ResponseVM? response)> GetAllUnitsAsync();

        /// <summary>
        /// جلب وحدة بالمعرف
        /// </summary>
        Task<(UnitDTO? model, ResponseVM? response)> GetUnitByIdAsync(Guid id);

        /// <summary>
        /// إضافة وحدة جديدة
        /// </summary>
        Task<ResponseVM> InsertUnitAsync(UnitDTO unit);

        /// <summary>
        /// تحديث وحدة موجودة
        /// </summary>
        Task<ResponseVM> UpdateUnitAsync(Guid id, UnitDTO unit);

        /// <summary>
        /// حذف وحدة
        /// </summary>
        Task<ResponseVM> DeleteUnitAsync(Guid id);
    }
}

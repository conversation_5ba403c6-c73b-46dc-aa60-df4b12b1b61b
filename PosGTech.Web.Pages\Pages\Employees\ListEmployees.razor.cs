﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Employees;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Employees
{
    public partial class ListEmployees
    {
        [Inject]
        IDialogService DialogService { get; set; }
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        IEnumerable<EmployeeDTO> employees = new List<EmployeeDTO>();
        EmployeeDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameEmplyeeForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
        }
        private bool FilterFunc1(EmployeeDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(EmployeeDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertEmployee>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertEmployee>(id != Guid.Empty ? "تعديل الموظف" : "إضافة الموظف", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(EmployeeDTO obj)
        {
            NameEmplyeeForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _unitOfWork.Employee.DeleteEmployeeAsync(obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _unitOfWork.Employee.GetAllEmployeesAsync();
            if (res.response == null) employees = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}
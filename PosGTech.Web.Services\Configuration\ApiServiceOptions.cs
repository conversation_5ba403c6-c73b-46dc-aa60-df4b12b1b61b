namespace PosGTech.Web.Services.Configuration
{
    /// <summary>
    /// خيارات تكوين خدمة API
    /// </summary>
    public class ApiServiceOptions
    {
        /// <summary>
        /// اسم القسم في appsettings.json
        /// </summary>
        public const string SectionName = "ApiService";

        /// <summary>
        /// الرابط الأساسي لـ API
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// مهلة انتظار الطلبات بالثواني
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// عدد محاولات إعادة الإرسال
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// تمكين تسجيل الطلبات
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// رؤوس HTTP الافتراضية
        /// </summary>
        public Dictionary<string, string> DefaultHeaders { get; set; } = new();

        /// <summary>
        /// تمكين ضغط البيانات
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// حجم الصفحة الافتراضي للترقيم
        /// </summary>
        public int DefaultPageSize { get; set; } = 10;

        /// <summary>
        /// الحد الأقصى لحجم الصفحة
        /// </summary>
        public int MaxPageSize { get; set; } = 100;
    }
}

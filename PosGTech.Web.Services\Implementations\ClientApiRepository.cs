using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Clients;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    public class ClientApiRepository : BaseApiRepository, IClientApiRepository
    {
        public ClientApiRepository(HttpClient httpClient, ILogger<ClientApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع العملاء
        /// </summary>
        public async Task<(IEnumerable<ClientDTO>? list, ResponseVM? response)> GetAllClientsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع العملاء");
                var response = await GetAsync("Clients/getAllClients");

                if (response.IsSuccessStatusCode)
                {
                    var clients = await response.Content.ReadFromJsonAsync<ClientDTO[]>();
                    return (clients, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العملاء");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع الموردين
        /// </summary>
        public async Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllSuppliersAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الموردين");
                var response = await GetAsync("Clients/getAllSuppliers");

                if (response.IsSuccessStatusCode)
                {
                    var suppliers = await response.Content.ReadFromJsonAsync<ClientCMDTO[]>();
                    return (suppliers, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الموردين");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع الزبائن
        /// </summary>
        public async Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllCustomersAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الزبائن");
                var response = await GetAsync("Clients/getAllCustomers");

                if (response.IsSuccessStatusCode)
                {
                    var customers = await response.Content.ReadFromJsonAsync<ClientCMDTO[]>();
                    return (customers, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الزبائن");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب عميل بالمعرف
        /// </summary>
        public async Task<(ClientDTO? model, ResponseVM? response)> GetClientByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب عميل بالمعرف: {Id}", id);
                var response = await GetAsync($"Clients/getClientById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var client = await response.Content.ReadFromJsonAsync<ClientDTO>();
                    return (client, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العميل بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        public async Task<ResponseVM> InsertClientAsync(ClientDTO client)
        {
            try
            {
                _logger.LogInformation("إضافة عميل جديد: {Name}", client.Name);
                var response = await PostAsJsonAsync("Clients/insertClient", client);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة العميل بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة العميل: {Name}", client.Name);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث عميل موجود
        /// </summary>
        public async Task<ResponseVM> UpdateClientAsync(Guid id, ClientDTO client)
        {
            try
            {
                _logger.LogInformation("تحديث العميل: {Id} - {Name}", id, client.Name);
                var response = await PutAsJsonAsync($"Clients/updateClient/{id}", client);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث العميل بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث العميل: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        public async Task<ResponseVM> DeleteClientAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف العميل: {Id}", id);
                var response = await DeleteAsync($"Clients/deleteClient/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف العميل بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف العميل: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب جميع الموردين للكومبو بوكس
        /// </summary>
        public async Task<(IEnumerable<ClientCMDTO>? list, ResponseVM? response)> GetAllSuppliersCMAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الموردين للكومبو بوكس");
                var response = await GetAsync("Clients/getAllSuppliers");

                if (response.IsSuccessStatusCode)
                {
                    var suppliers = await response.Content.ReadFromJsonAsync<IEnumerable<ClientCMDTO>>();
                    return (suppliers, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الموردين للكومبو بوكس");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}

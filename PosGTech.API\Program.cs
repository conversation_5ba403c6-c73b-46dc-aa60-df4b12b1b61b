using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using PosGTech.API.Services;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Mapper;
using PosGTech.Models;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Authorization;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
//-------------------------------------------------------------------------------------------------------------
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.WriteIndented = false;
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();



//-------------------------------------------------------------------------------------------------------------

//Local
//DbCon
builder.Services.AddDbContext<ApplicationDbContext>(options =>
 options.UseSqlServer(builder.Configuration.GetConnectionString("Local"), b => b.MigrationsAssembly("PosGTech.API"))

 );
//-------------------------------------------------------------------------------------------------------------

builder.Services.Configure<Jwt>(builder.Configuration.GetSection("JWT"));
//-------------------------------------------------------------------------------------------------------------

builder.Services.AddIdentity<User, Role>(x => x.SignIn.RequireConfirmedEmail = false)
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders(); // Important to use GenerateEmailConfirmationTokenAsync
//-------------------------------------------------------------------------------------------------------------

//-------------------------------------------------------------------------------------------------------------
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
    .AddJwtBearer(o =>
    {
        o.RequireHttpsMetadata = false;
        o.SaveToken = false;
        o.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidIssuer = builder.Configuration["JWT:Issuer"],
            ValidAudience = builder.Configuration["JWT:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["JWT:Key"])),
            ClockSkew = TimeSpan.FromMinutes(5) // هامش أمان 5 دقائق للتوقيت
        };
    });



// ********************* Size of files on email sending setting ***************************
builder.Services.Configure<FormOptions>(o =>
{
    o.ValueLengthLimit = int.MaxValue;
    o.MultipartBodyLengthLimit = int.MaxValue;
    o.MemoryBufferThreshold = int.MaxValue;
});

// ************ To change the claims for roles immediately and dont save them to browser **********
builder.Services.Configure<SecurityStampValidatorOptions>(options =>
{
    options.ValidationInterval = TimeSpan.Zero;
});
//-------------------------------------------------------------------------------------------------------------

builder.Services.Configure<IdentityOptions>(options =>
{
    options.Password.RequiredLength = 3;
    options.Password.RequiredUniqueChars = 1;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireUppercase = false;
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(10);
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
    options.SignIn.RequireConfirmedAccount = false;
    options.User.RequireUniqueEmail = true;
});

//-------------------------------------------------------------------------------------------------------------

// إعداد نظام التفويض المبني على الصلاحيات
builder.Services.AddAuthorization(options =>
{
    // سياسة عامة للتحقق من وجود أي صلاحية
    options.AddPolicy("permission", policy =>
        policy.Requirements.Add(new PermissionRequirement("any")));

    // إضافة سياسة لكل صلاحية في النظام
    foreach (var permission in PermissionConstants.GetAllPermissions())
    {
        options.AddPolicy(permission, policy =>
            policy.Requirements.Add(new PermissionRequirement(permission)));
    }
});

// تسجيل معالج الصلاحيات
builder.Services.AddScoped<IAuthorizationHandler, PermissionHandler>();

//-------------------------------------------------------------------------------------------------------------

builder.Services.AddScoped(typeof(ReportService));
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddAutoMapper(x => x.AddProfile(new MappingProfile()));
builder.Services.AddScoped<SeedData>();

//-------------------------------------------------------------------------------------------------------------
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin",
        policy =>
        {
            policy.WithOrigins("https://localhost:7151", "http://localhost:7151", "https://localhost:7241", "http://localhost:7241")
                  .AllowAnyHeader()
                  .AllowAnyMethod()
                  .AllowCredentials();
        });
});


var app = builder.Build();
await SeedDatabase();
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
// إضافة دعم Static Files لعرض الصور المرفوعة
app.UseStaticFiles();

app.UseRouting();
app.UseCors("AllowSpecificOrigin");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
async Task SeedDatabase()
{
    using (var scope = app.Services.CreateScope())
    {
        var dbInitializer = scope.ServiceProvider.GetRequiredService<SeedData>();
        await dbInitializer.init();
    }
}

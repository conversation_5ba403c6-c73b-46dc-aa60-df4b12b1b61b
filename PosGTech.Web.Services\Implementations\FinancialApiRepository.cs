using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للعمليات المالية - يحتوي على جميع العمليات المطلوبة للتعامل مع العمليات المالية
    /// </summary>
    public class FinancialApiRepository : BaseApiRepository, IFinancialApiRepository
    {
        public FinancialApiRepository(HttpClient httpClient, ILogger<FinancialApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع العمليات المالية
        /// </summary>
        public async Task<(IEnumerable<FinancialDTO>? list, ResponseVM? response)> GetAllFinancialsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع العمليات المالية");
                var response = await GetAsync("Financials/getAllFinancials");

                if (response.IsSuccessStatusCode)
                {
                    var financials = await response.Content.ReadFromJsonAsync<FinancialDTO[]>();
                    return (financials, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العمليات المالية");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب عملية مالية بالمعرف
        /// </summary>
        public async Task<(FinancialDTO? model, ResponseVM? response)> GetFinancialByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب العملية المالية بالمعرف: {Id}", id);
                var response = await GetAsync($"Financials/getFinancialById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var financial = await response.Content.ReadFromJsonAsync<FinancialDTO>();
                    return (financial, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العملية المالية بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة عملية مالية جديدة
        /// </summary>
        public async Task<ResponseVM> InsertFinancialAsync(FinancialDTO financial)
        {
            try
            {
                _logger.LogInformation("إضافة عملية مالية جديدة");
                var response = await PostAsJsonAsync("Financials/insertFinancial", financial);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة العملية المالية بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة العملية المالية");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث عملية مالية موجودة
        /// </summary>
        public async Task<ResponseVM> UpdateFinancialAsync(Guid id, FinancialDTO financial)
        {
            try
            {
                _logger.LogInformation("تحديث العملية المالية: {Id}", id);
                var response = await PutAsJsonAsync($"Financials/updateFinancial/{id}", financial);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث العملية المالية بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث العملية المالية: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف عملية مالية
        /// </summary>
        public async Task<ResponseVM> DeleteFinancialAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف العملية المالية: {Id}", id);
                var response = await DeleteAsync($"Financials/deleteFinancial/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف العملية المالية بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف العملية المالية: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}

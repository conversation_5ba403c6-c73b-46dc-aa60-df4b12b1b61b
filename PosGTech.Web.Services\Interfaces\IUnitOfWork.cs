namespace PosGTech.Web.Services.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        // الوحدات الموجودة حالياً
        ICategoryApiRepository Category { get; }
        IClientApiRepository Client { get; }
        IStoreApiRepository Store { get; }
        IItemApiRepository Item { get; }
        IExpenseApiRepository Expense { get; }
        IEmployeeApiRepository Employee { get; }

        // الوحدات الأساسية الجديدة
        IPurchaseApiRepository Purchase { get; }
        ISellApiRepository Sell { get; }
        IReceiptApiRepository Receipt { get; }
        IFinancialApiRepository Financial { get; }
        ITreasuryApiRepository Treasury { get; }
        IUnitApiRepository Unit { get; }

        // الوحدات المتقدمة الجديدة
        IInventoryApiRepository Inventory { get; }
        IConsumedApiRepository Consumed { get; }
        IStoreItemApiRepository StoreItem { get; }

        // وحدات الإدارة الجديدة
        IUserApiRepository User { get; }
        IRoleApiRepository Role { get; }
        IShopSettingsApiRepository ShopSettings { get; }
    }
}

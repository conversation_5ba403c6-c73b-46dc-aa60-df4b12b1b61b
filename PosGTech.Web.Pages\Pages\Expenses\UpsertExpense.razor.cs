﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Expenses
{
    public partial class UpsertExpense
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        ExpenseDTO Expense = new();
        [Inject]
        IUnitOfWork _unitOfWork { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _unitOfWork.Expense.GetExpenseByIdAsync(id);
                if (res.response == null)
                {
                    Expense = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }


            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _unitOfWork.Expense.InsertExpenseAsync(Expense);
            else
                response = await _unitOfWork.Expense.UpdateExpenseAsync(id, Expense);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

    }
}
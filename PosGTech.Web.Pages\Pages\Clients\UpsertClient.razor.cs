﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Clients;
using PosGTech.Web.Pages.Pages.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Clients;

public partial class UpsertClient
{
    [Inject]
    IDialogService DialogService { get; set; }
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter]
    public Guid id { get; set; }
    ClientDTO client = new();
    bool isEdit = false;
    [Inject]
    IUnitOfWork _unitOfWork { get; set; }
    protected override async Task OnInitializedAsync()
    {
        if (id != Guid.Empty)
        {
            await LoadingData();
        }
    }
    async void Upsert()
    {
        if (client.IsSupplier == false && client.IsCustomer == false)
        {
            _snackbar.Add("يرجى تحديد ما إذا كان العميل زبون أم مورد أم كلاهما", Severity.Error);
            return;
        }
        ResponseVM response;
        if (id == Guid.Empty)
            response = await _unitOfWork.Client.InsertClientAsync(client);
        else
            response = await _unitOfWork.Client.UpdateClientAsync(id, client);

        if (response.State)
        {
            _snackbar.Add(response.Message, Severity.Success);
            MudDialog.Close(client.Name);
        }
        else
        {
            _snackbar.Add(response.Message, Severity.Error);
        }
    }
    void Cancel()
    {
        if (isEdit) MudDialog.Close(client.Name);
        else MudDialog.Cancel();
    }
    async void AddReceipt(Guid financialId)
    {
        var parameters = new DialogParameters<UpsertReceipt>();
        parameters.Add(x => x.id, Guid.Empty);
        parameters.Add(x => x.financialId, financialId);
        parameters.Add(x => x.Client, new() { Id = id, Name = client.Name });
        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertReceipt>("إضافة الإيصال", parameters, options).Result;
        if ((bool?)result.Data == true)
        {
            await LoadingData();
            isEdit = true;
        }
    }
    async Task LoadingData()
    {
        var res = await _unitOfWork.Client.GetClientByIdAsync(id);
        if (res.response == null)
        {
            client = res.model; StateHasChanged();
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }

}
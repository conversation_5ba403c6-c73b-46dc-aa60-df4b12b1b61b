﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Users;
using PosGTech.Web.Services;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Receipts;

public partial class UpsertReceiptClients
{
    [Inject]
    IdentityAuthenticationStateProvider _auth { get; set; }
    [Parameter]
    public IEnumerable<UserTreasuryCMDTO> UserTreasury { get; set; } = new List<UserTreasuryCMDTO>();
    [Parameter]
    public IEnumerable<UserCMDTO> Users { get; set; } = new List<UserCMDTO>();
    IEnumerable<ClientCMDTO> Clients = new List<ClientCMDTO>();
    [Inject]
    IUnitOfWork _unitOfWork { get; set; }
    UserCMDTO _crruntUser;
    [Parameter]
    public EventCallback<ReceiptDTO> ReceiptChanged { get; set; }
    [Parameter]
    public ReceiptDTO Receipt
    {
        get => _receipt;
        set
        {
            if (value == _receipt)
                return;

            _receipt = value;
            if (ReceiptChanged.HasDelegate)
            {
                ReceiptChanged.InvokeAsync(_receipt);
            }
        }
    }
    private ReceiptDTO _receipt;
    private decimal _lastValue = 0;
    protected override async Task OnInitializedAsync()
    {
        var resClient = await _unitOfWork.Client.GetAllSuppliersCMAsync();
        if (resClient.response == null) Clients = resClient.list ?? new List<ClientCMDTO>();
        if (Receipt.Id == Guid.Empty)
        {
            _lastValue = 0;
            Receipt.IsExchange = true;
            var auth = await _auth.GetAuthenticationStateAsync();
            var userIdClaim = auth.User.Claims.First(x => x.Type == "id").Value;
            _crruntUser = Users.First(x => x.Id == Guid.Parse(userIdClaim));
            Receipt.UserTreasury = UserTreasury.First(x => x.User.Id == _crruntUser.Id && x.Treasury.Name == "النقدية");
            StateHasChanged();
        }
        else
        {
            _lastValue = Receipt.IsExchange == true ? Receipt.Value : -Receipt.Value;
            _crruntUser = Receipt.UserTreasury.User;
            StateHasChanged();
        }

    }
    private async Task<IEnumerable<UserTreasuryCMDTO>> SearchUserTreasury(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id);
        return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id).Where(x => x.Treasury.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    private async Task<IEnumerable<ClientCMDTO>> SearchClient(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Clients;
        return Clients.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    private async Task<IEnumerable<UserCMDTO>> SearchUser(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Users;
        return Users.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    void ValueChange(decimal value)
    {
        Receipt.Value = value;
        if (value < 0)
        {
            Receipt.Value = 0;
            _snackbar.Add("الرجاء ادخال قيمة صحيحة", Severity.Error);
            return;
        }
        if (Receipt.UserTreasury == null)
        {
            Receipt.Value = 0;
            _snackbar.Add("الرجاء اختيار خزينة", Severity.Error);
            return;
        }
        if (Receipt.UserTreasury.Balance + _lastValue < value && Receipt.IsExchange == true)
        {
            Receipt.Value = Receipt.UserTreasury.Balance + _lastValue;
            _snackbar.Add("تجاوزت القيمة الموجودة في الخزينة", Severity.Error);
            return;
        }

    }
}
# خطة اختبار نظام التنقل بالتركيز - صفحة فواتير المشتريات

## نظرة عامة
هذا المستند يحتوي على خطة اختبار شاملة لنظام التنقل بالتركيز والانتقال بزر Enter في صفحة فواتير المشتريات.

## أهداف الاختبار
1. التأكد من عمل التنقل بزر Enter بين جميع الحقول
2. التحقق من صحة تسلسل التركيز
3. اختبار السلوك الذكي للنظام (تخطي الحقول الاختيارية)
4. التأكد من عمل التحقق من صحة البيانات
5. اختبار العودة التلقائية للحقول بعد إضافة الأصناف

## سيناريوهات الاختبار

### 1. اختبار التنقل الأساسي

#### Test Case 1.1: التنقل المتسلسل بين جميع الحقول
**الخطوات:**
1. افتح صفحة فاتورة مشتريات جديدة
2. تأكد من أن التركيز على حقل المخازن (TabIndex: 1)
3. اضغط Enter
4. تحقق من انتقال التركيز لحقل المورد (TabIndex: 2)
5. كرر العملية لجميع الحقول بالترتيب

**النتيجة المتوقعة:**
- التنقل السلس بين الحقول بالترتيب الصحيح: مخازن → مورد → صنف → وحدة → تاريخ صلاحية → كمية → سعر شراء → سعر بيع → زر إضافة

#### Test Case 1.2: التنقل مع تخطي تاريخ الصلاحية
**الخطوات:**
1. اختر صنف لا يتطلب تاريخ صلاحية
2. انتقل لحقل الوحدة واضغط Enter
3. تحقق من تخطي حقل تاريخ الصلاحية والانتقال مباشرة لحقل الكمية

**النتيجة المتوقعة:**
- تخطي حقل تاريخ الصلاحية تلقائياً عندما لا يكون مطلوباً

### 2. اختبار التحقق من صحة البيانات

#### Test Case 2.1: منع الانتقال عند عدم اختيار المخزن
**الخطوات:**
1. اترك حقل المخازن فارغاً
2. اضغط Enter
3. تحقق من ظهور رسالة خطأ وعدم الانتقال للحقل التالي

**النتيجة المتوقعة:**
- ظهور رسالة "يجب اختيار المخزن"
- عدم الانتقال للحقل التالي

#### Test Case 2.2: منع الانتقال عند عدم اختيار الصنف
**الخطوات:**
1. املأ حقل المخازن والمورد
2. اترك حقل الصنف فارغاً واضغط Enter
3. تحقق من ظهور رسالة خطأ

**النتيجة المتوقعة:**
- ظهور رسالة "يجب اختيار الصنف"
- عدم الانتقال للحقل التالي

#### Test Case 2.3: منع الانتقال عند إدخال كمية غير صحيحة
**الخطوات:**
1. املأ جميع الحقول حتى حقل الكمية
2. أدخل كمية صفر أو سالبة
3. اضغط Enter

**النتيجة المتوقعة:**
- ظهور رسالة "يجب إدخال كمية صحيحة"
- عدم الانتقال للحقل التالي

### 3. اختبار إضافة الأصناف

#### Test Case 3.1: إضافة صنف بزر Enter
**الخطوات:**
1. املأ جميع الحقول المطلوبة
2. انتقل لزر "إضافة الصنف"
3. اضغط Enter
4. تحقق من إضافة الصنف وعودة التركيز لحقل الصنف

**النتيجة المتوقعة:**
- إضافة الصنف بنجاح
- مسح حقول الإدخال
- عودة التركيز لحقل الصنف تلقائياً

#### Test Case 3.2: العودة التلقائية بعد إضافة عدة أصناف
**الخطوات:**
1. أضف صنف أول باستخدام Enter
2. أضف صنف ثاني باستخدام Enter
3. تحقق من عودة التركيز لحقل الصنف في كل مرة

**النتيجة المتوقعة:**
- عودة التركيز لحقل الصنف بعد كل إضافة
- إمكانية إضافة أصناف متعددة بسرعة

### 4. اختبار السيناريوهات المعقدة

#### Test Case 4.1: التنقل مع أصناف لها تاريخ صلاحية
**الخطوات:**
1. اختر صنف يتطلب تاريخ صلاحية
2. انتقل من حقل الوحدة بـ Enter
3. تحقق من عدم تخطي حقل تاريخ الصلاحية
4. أدخل تاريخ صلاحية واضغط Enter

**النتيجة المتوقعة:**
- عدم تخطي حقل تاريخ الصلاحية
- الانتقال الطبيعي لحقل الكمية بعد إدخال التاريخ

#### Test Case 4.2: التنقل مع تغيير نوع الصنف
**الخطوات:**
1. اختر صنف لا يتطلب تاريخ صلاحية
2. انتقل لحقل الوحدة
3. غير الصنف لآخر يتطلب تاريخ صلاحية
4. اضغط Enter من حقل الوحدة

**النتيجة المتوقعة:**
- الانتقال لحقل تاريخ الصلاحية (عدم التخطي)

### 5. اختبار الحالات الحدية

#### Test Case 5.1: التنقل مع حقول معطلة
**الخطوات:**
1. افتح فاتورة موجودة (حقل المخزن معطل)
2. جرب التنقل بـ Enter
3. تحقق من تخطي الحقول المعطلة

**النتيجة المتوقعة:**
- تخطي الحقول المعطلة تلقائياً

#### Test Case 5.2: التنقل عند نهاية التسلسل
**الخطوات:**
1. املأ جميع الحقول
2. اضغط Enter من زر إضافة الصنف
3. تحقق من العودة لحقل الصنف (بداية الدورة)

**النتيجة المتوقعة:**
- العودة لحقل الصنف لبدء دورة جديدة

### 6. اختبار الأداء والاستقرار

#### Test Case 6.1: اختبار الضغط المتكرر على Enter
**الخطوات:**
1. اضغط Enter بسرعة عدة مرات
2. تحقق من عدم حدوث أخطاء أو تعليق

**النتيجة المتوقعة:**
- عدم حدوث أخطاء
- استجابة سلسة للنظام

#### Test Case 6.2: اختبار مع بيانات كبيرة
**الخطوات:**
1. افتح فاتورة تحتوي على عدد كبير من الأصناف
2. جرب التنقل بـ Enter
3. تحقق من عدم تأثر الأداء

**النتيجة المتوقعة:**
- أداء سريع ومستقر
- عدم تأثر التنقل بحجم البيانات

## معايير النجاح

### 1. الوظائف الأساسية
- ✅ التنقل بـ Enter يعمل بين جميع الحقول
- ✅ تسلسل التركيز صحيح (1-9)
- ✅ تخطي الحقول الاختيارية يعمل
- ✅ التحقق من صحة البيانات يعمل
- ✅ إضافة الأصناف بـ Enter تعمل
- ✅ العودة التلقائية لحقل الصنف تعمل

### 2. تجربة المستخدم
- ✅ التنقل سلس وطبيعي
- ✅ رسائل الخطأ واضحة ومفيدة
- ✅ لا توجد تأخيرات ملحوظة
- ✅ النظام بديهي وسهل الاستخدام

### 3. الاستقرار
- ✅ لا توجد أخطاء في وحدة التحكم
- ✅ لا يحدث تعليق أو تجمد
- ✅ الذاكرة مستقرة (لا توجد تسريبات)
- ✅ يعمل مع جميع المتصفحات المدعومة

## تقرير الاختبار

### تاريخ الاختبار: [يتم ملؤه عند التنفيذ]
### المختبر: [يتم ملؤه عند التنفيذ]
### البيئة: [يتم ملؤه عند التنفيذ]

### النتائج:
| Test Case | الحالة | ملاحظات |
|-----------|---------|----------|
| 1.1 | ⏳ | |
| 1.2 | ⏳ | |
| 2.1 | ⏳ | |
| 2.2 | ⏳ | |
| 2.3 | ⏳ | |
| 3.1 | ⏳ | |
| 3.2 | ⏳ | |
| 4.1 | ⏳ | |
| 4.2 | ⏳ | |
| 5.1 | ⏳ | |
| 5.2 | ⏳ | |
| 6.1 | ⏳ | |
| 6.2 | ⏳ | |

### الرموز:
- ✅ نجح
- ❌ فشل
- ⏳ لم يتم الاختبار بعد
- ⚠️ نجح مع ملاحظات

## الخطوات التالية
1. تنفيذ جميع حالات الاختبار
2. توثيق أي مشاكل أو تحسينات مطلوبة
3. إجراء اختبارات إضافية حسب الحاجة
4. الحصول على موافقة المستخدم النهائي

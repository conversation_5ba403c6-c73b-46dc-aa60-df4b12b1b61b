using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Blazored.LocalStorage;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمخازن - يحتوي على جميع العمليات المطلوبة للتعامل مع المخازن
    /// </summary>
    public class StoreApiRepository : BaseApiRepository, IStoreApiRepository
    {
        public StoreApiRepository(HttpClient httpClient, ILogger<StoreApiRepository> logger, ILocalStorageService localStorage)
            : base(httpClient, logger, localStorage)
        {
        }

        /// <summary>
        /// جلب جميع المخازن
        /// </summary>
        public async Task<(IEnumerable<StoreDTO>? list, ResponseVM? response)> GetAllStoresAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع المخازن");

                var httpResponse = await GetAsync("Stores/getAllStores");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var stores = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<StoreDTO>>();
                    _logger.LogInformation("تم جلب {Count} مخزن بنجاح", stores?.Count() ?? 0);
                    return (stores, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب المخازن: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب المخازن: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المخازن");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب جميع المخازن للقوائم المنسدلة
        /// </summary>
        public async Task<(IEnumerable<StoreCMDTO>? list, ResponseVM? response)> GetAllStoresCMBAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب المخازن للقوائم المنسدلة");

                var httpResponse = await GetAsync("Stores/getAllStoresCMB");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var stores = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<StoreCMDTO>>();
                    _logger.LogInformation("تم جلب {Count} مخزن للقوائم المنسدلة بنجاح", stores?.Count() ?? 0);
                    return (stores, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب المخازن للقوائم المنسدلة: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب المخازن: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المخازن للقوائم المنسدلة");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب مخزن بالمعرف
        /// </summary>
        public async Task<(StoreDTO? model, ResponseVM? response)> GetStoreByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب المخزن بالمعرف: {StoreId}", id);

                var httpResponse = await GetAsync($"Stores/getStoreById/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var store = await httpResponse.Content.ReadFromJsonAsync<StoreDTO>();
                    _logger.LogInformation("تم جلب المخزن بالمعرف {StoreId} بنجاح", id);
                    return (store, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب المخزن بالمعرف {StoreId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب المخزن: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المخزن بالمعرف: {StoreId}", id);
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// إضافة مخزن جديد
        /// </summary>
        public async Task<ResponseVM> InsertStoreAsync(StoreDTO store)
        {
            try
            {
                _logger.LogInformation("بدء عملية إضافة مخزن جديد: {StoreName}", store.Name);

                var httpResponse = await PostAsJsonAsync("Stores/insertStore", store);

                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم إضافة المخزن {StoreName} بنجاح", store.Name);
                    return result ?? new ResponseVM { Message = "تم إضافة المخزن بنجاح", State = true };
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في إضافة المخزن {StoreName}: {StatusCode} - {Error}", store.Name, httpResponse.StatusCode, errorContent);
                    return new ResponseVM { Message = errorContent, State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة المخزن: {StoreName}", store.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// تحديث مخزن موجود
        /// </summary>
        public async Task<ResponseVM> UpdateStoreAsync(Guid id, StoreDTO store)
        {
            try
            {
                _logger.LogInformation("بدء عملية تحديث المخزن: {StoreId} - {StoreName}", id, store.Name);

                var httpResponse = await PutAsJsonAsync($"Stores/updateStore/{id}", store);

                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم تحديث المخزن {StoreId} - {StoreName} بنجاح", id, store.Name);
                    return result ?? new ResponseVM { Message = "تم تحديث المخزن بنجاح", State = true };
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في تحديث المخزن {StoreId} - {StoreName}: {StatusCode} - {Error}", id, store.Name, httpResponse.StatusCode, errorContent);
                    return new ResponseVM { Message = errorContent, State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث المخزن: {StoreId} - {StoreName}", id, store.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// حذف مخزن
        /// </summary>
        public async Task<ResponseVM> DeleteStoreAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية حذف المخزن: {StoreId}", id);

                var httpResponse = await DeleteAsync($"Stores/deleteStore/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم حذف المخزن {StoreId} بنجاح", id);
                    return result ?? new ResponseVM { Message = "تم حذف المخزن بنجاح", State = true };
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في حذف المخزن {StoreId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorContent);
                    return new ResponseVM { Message = errorContent, State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء حذف المخزن: {StoreId}", id);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// جلب جميع إعدادات المخازن
        /// </summary>
        public async Task<(IEnumerable<StoreSettingsDTO>? list, ResponseVM? response)> GetAllStoreSettingsAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع إعدادات المخازن");

                var httpResponse = await GetAsync("Stores/getAllStoreSettings");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var storeSettings = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<StoreSettingsDTO>>();
                    _logger.LogInformation("تم جلب {Count} إعداد مخزن بنجاح", storeSettings?.Count() ?? 0);
                    return (storeSettings, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(errorContent);
                    _logger.LogWarning("فشل في جلب إعدادات المخازن: {StatusCode} - {Error}", httpResponse.StatusCode, errorResponse?.Message);
                    return (null, errorResponse ?? new ResponseVM { Message = "خطأ في جلب إعدادات المخازن", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب إعدادات المخازن");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب إعدادات مخزن بالمعرف
        /// </summary>
        public async Task<(StoreSettingsDTO? model, ResponseVM? response)> GetStoreSettingsByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب إعدادات المخزن بالمعرف: {StoreId}", id);

                var httpResponse = await GetAsync($"Stores/getStoreSettingsById/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var storeSettings = await httpResponse.Content.ReadFromJsonAsync<StoreSettingsDTO>();
                    _logger.LogInformation("تم جلب إعدادات المخزن بالمعرف {StoreId} بنجاح", id);
                    return (storeSettings, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(errorContent);
                    _logger.LogWarning("فشل في جلب إعدادات المخزن بالمعرف {StoreId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorResponse?.Message);
                    return (null, errorResponse ?? new ResponseVM { Message = "خطأ في جلب إعدادات المخزن", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب إعدادات المخزن بالمعرف: {StoreId}", id);
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// إضافة إعدادات مخزن جديدة
        /// </summary>
        public async Task<ResponseVM> InsertStoreSettingsAsync(StoreSettingsDTO storeSettings)
        {
            try
            {
                _logger.LogInformation("بدء عملية إضافة إعدادات مخزن جديدة: {StoreName}", storeSettings.Name);

                var httpResponse = await PostAsJsonAsync("Stores/insertStoreSettings", storeSettings);

                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم إضافة إعدادات المخزن {StoreName} بنجاح", storeSettings.Name);
                    return result ?? new ResponseVM { Message = "تم إضافة إعدادات المخزن بنجاح", State = true };
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في إضافة إعدادات المخزن {StoreName}: {StatusCode} - {Error}", storeSettings.Name, httpResponse.StatusCode, errorContent);
                    return new ResponseVM { Message = errorContent, State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة إعدادات المخزن: {StoreName}", storeSettings.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// تحديث إعدادات مخزن موجودة
        /// </summary>
        public async Task<ResponseVM> UpdateStoreSettingsAsync(Guid id, StoreSettingsDTO storeSettings)
        {
            try
            {
                _logger.LogInformation("بدء عملية تحديث إعدادات المخزن: {StoreId} - {StoreName}", id, storeSettings.Name);

                var httpResponse = await PutAsJsonAsync($"Stores/updateStoreSettings/{id}", storeSettings);

                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم تحديث إعدادات المخزن {StoreId} - {StoreName} بنجاح", id, storeSettings.Name);
                    return result ?? new ResponseVM { Message = "تم تحديث إعدادات المخزن بنجاح", State = true };
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في تحديث إعدادات المخزن {StoreId} - {StoreName}: {StatusCode} - {Error}", id, storeSettings.Name, httpResponse.StatusCode, errorContent);
                    return new ResponseVM { Message = errorContent, State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث إعدادات المخزن: {StoreId} - {StoreName}", id, storeSettings.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }
    }
}

# إصلاح مشكلة تركيز حقل العنصر/المنتج في فاتورة الشراء

## المشكلة الأصلية
عند التركيز على حقل العنصر/المنتج وظهور قائمة منسدلة، بعد تحديد عنصر باستخدام مفتاح الإدخال (Enter)، كان النظام يتخطى الحقل التالي بدلاً من تعبئته والانتقال إليه بشكل صحيح.

## الحلول المطبقة

### 1. تحديث دالة `HandleItemKeyDown`
**الملف**: `PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor.cs`
**الأسطر**: 2236-2273

**التحسينات**:
- إزالة التداخل في استدعاء `SelectItem`
- تحسين منطق البحث عن العناصر
- إضافة معالجة أفضل للحالات الفارغة
- تحسين تدفق التنقل بين الحقول

```csharp
private async Task HandleItemKeyDown(KeyboardEventArgs e)
{
    if (e.Key == "Enter")
    {
        // التحقق من وجود نص في الحقل
        if (string.IsNullOrEmpty(ItemForAdd?.Text))
        {
            // إذا لم يكن هناك نص، انتقل للحقل التالي مباشرة
            await HandleEnterKeyPress(e, 3);
            return;
        }

        // البحث عن العنصر في القائمة المعروضة
        var foundInDisplayed = _displayedItems?.FirstOrDefault(x =>
            x.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) ||
            x.ItemNums?.Any(num => num.Barcode == ItemForAdd.Text) == true);

        if (foundInDisplayed != null)
        {
            // تحديد العنصر المختار
            await OnItemSelected(foundInDisplayed);
            
            // تأخير قصير للسماح بتحديث الواجهة
            await Task.Delay(50);
            
            // الانتقال للحقل التالي
            await HandleEnterKeyPress(e, 3);
        }
        else
        {
            // إذا لم يتم العثور على العنصر، عرض رسالة خطأ
            _snackbar.Add("لم يتم العثور على الصنف المطلوب", Severity.Warning);
        }
    }
}
```

### 2. تحديث دالة `OnItemSelected`
**الملف**: `PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor.cs`
**الأسطر**: 993-1038

**التحسينات**:
- إضافة تحديث مباشر لنص الحقل
- تحسين مسح البيانات عند إلغاء الاختيار
- ضمان تزامن البيانات بين المتغيرات المختلفة

```csharp
private async Task OnItemSelected(ItemCMDTO? item)
{
    selectedPurchaseItem.Item = item;
    selectedPurchaseItem.ItemUnit = null;

    if (selectedPurchaseItem.Item != null)
    {
        var fullItem = items.FirstOrDefault(x => x.Id == selectedPurchaseItem.Item.Id);
        if (fullItem != null)
        {
            selectedPurchaseItem.Price = fullItem.CostPrice;
            selectedPurchaseItem.ItemUnit = fullItem.ItemUnits.FirstOrDefault(x => x.IsBasicUnit);
            _dateExp = fullItem.IsHaveExp ? DateTime.Now : null;

            // إضافة سعر البيع عند اختيار الصنف
            SalePriceChange = selectedPurchaseItem.ItemUnit?.SalePrice ?? 0;

            // تحديث النص المعروض ليطابق الصنف المحدد
            _searchText = item.Name;
            
            // تحديث قيمة الحقل لضمان عرض الاسم الصحيح
            if (ItemForAdd != null)
            {
                ItemForAdd.Text = item.Name;
            }
        }
    }
    else
    {
        // إذا تم مسح الاختيار، مسح النص أيضاً
        _searchText = string.Empty;
        // مسح سعر البيع عند مسح الاختيار
        SalePriceChange = 0;
        
        // مسح نص الحقل
        if (ItemForAdd != null)
        {
            ItemForAdd.Text = string.Empty;
        }
    }

    StateHasChanged();
}
```

### 3. تبسيط دالة `SelectItem`
**الملف**: `PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor.cs`
**الأسطر**: 235-248

**التحسينات**:
- تحويل المعالجة للدالة الجديدة `HandleItemKeyDown`
- الحفاظ على التوافق مع الكود القديم
- تجنب التداخل في المعالجة

### 4. تحديث دالة `ValidateCurrentField`
**الملف**: `PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor.cs`
**الأسطر**: 2360-2363

**التحسينات**:
- السماح بالانتقال من حقل العنصر حتى لو لم يتم اختيار صنف
- نقل التحقق من الصنف لمرحلة إضافة العنصر
- تحسين تدفق التنقل

## النتائج المتوقعة

### السلوك الجديد المحسن:
1. **عند ظهور القائمة المنسدلة**: يمكن للمستخدم التنقل بالأسهم واختيار عنصر
2. **عند الضغط على Enter**: 
   - إذا كان هناك نص مطابق، يتم اختيار العنصر وملء الحقل
   - يتم الانتقال تلقائياً للحقل التالي (الوحدة)
   - إذا لم يكن هناك نص، ينتقل للحقل التالي مباشرة
3. **عند اختيار عنصر**: يتم ملء جميع البيانات المرتبطة (السعر، الوحدة، إلخ)
4. **التنقل السلس**: لا يوجد تخطي للحقول أو تداخل في المعالجة

### المزايا الإضافية:
- **أداء محسن**: تقليل عدد استدعاءات الدوال المتكررة
- **استقرار أكبر**: تجنب التداخل في معالجة الأحداث
- **تجربة مستخدم أفضل**: تدفق طبيعي ومنطقي للتنقل
- **معالجة أخطاء محسنة**: رسائل واضحة عند عدم العثور على العناصر

## اختبار الحل

### خطوات الاختبار:
1. فتح صفحة إنشاء فاتورة شراء جديدة
2. التنقل لحقل العنصر/المنتج
3. كتابة جزء من اسم منتج موجود
4. الضغط على Enter
5. التحقق من:
   - ملء الحقل بالاسم الصحيح
   - الانتقال لحقل الوحدة
   - ملء البيانات المرتبطة (السعر، الوحدة الأساسية)

### حالات اختبار إضافية:
- اختبار البحث بالباركود
- اختبار الحقل الفارغ
- اختبار عنصر غير موجود
- اختبار التنقل بالأسهم في القائمة المنسدلة

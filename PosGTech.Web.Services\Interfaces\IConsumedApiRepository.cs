using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Consumeds;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IConsumedApiRepository
    {
        /// <summary>
        /// جلب جميع المستهلكات
        /// </summary>
        Task<(IEnumerable<ConsumedDTO>? list, ResponseVM? response)> GetAllConsumedsAsync();

        /// <summary>
        /// جلب مستهلك بالمعرف
        /// </summary>
        Task<(ConsumedDTO? model, ResponseVM? response)> GetConsumedByIdAsync(Guid id);

        /// <summary>
        /// إضافة مستهلك جديد
        /// </summary>
        Task<ResponseVM> InsertConsumedAsync(ConsumedDTO consumed);

        /// <summary>
        /// تحديث مستهلك موجود
        /// </summary>
        Task<ResponseVM> UpdateConsumedAsync(Guid id, ConsumedDTO consumed);

        /// <summary>
        /// حذف مستهلك
        /// </summary>
        Task<ResponseVM> DeleteConsumedAsync(Guid id);

        /// <summary>
        /// جلب عناصر المستهلك
        /// </summary>
        Task<(IEnumerable<ConsumedItemDTO>? list, ResponseVM? response)> GetConsumedItemsAsync(Guid consumedId);
    }
}
